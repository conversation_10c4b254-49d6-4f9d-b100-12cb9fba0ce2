# 🤖 KiotViet RAG Chatbot

A production-ready RAG chatbot integrated with KiotViet MCP server using **mcp-use** framework.

## 🌟 Features

- 🧠 **Intelligent RAG System**: Combines knowledge base with real-time data
- 🔗 **MCP Integration**: Seamless connection to KiotViet MCP server via mcp-use
- 🤖 **Smart Query Routing**: Automatically decides between RAG and MCP tools
- 🌐 **Multiple Interfaces**: CLI, Web UI (Streamlit), and REST API
- 🔄 **Streaming Support**: Real-time response streaming
- 📊 **Production Ready**: Monitoring, logging, error handling
- 🐳 **Docker Support**: Containerized deployment

## 🏗️ Architecture

```
User Query → RAG Chatbot → mcp-use → KiotViet MCP Server → KiotViet API
     ↓              ↓           ↓
Vector Search   Intent      Tool Calling
+ Knowledge    Analysis    + Function
  Base                      Execution
```

## 📦 Quick Start

### Prerequisites

- Python 3.11+
- KiotViet MCP Server running at `http://localhost:8000`
- OpenAI API key

### Installation

```bash
# Clone the project
git clone <your-repo-url>
cd kiotviet-rag-chatbot

# Install dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env
# Edit .env with your API keys
```

### Run

```bash
# Quick test
python scripts/quick_test.py

# CLI interface
python -m src.cli

# Web interface
streamlit run src/web/streamlit_app.py

# API server
python -m src.api.main
```

## 📁 Project Structure

```
kiotviet-rag-chatbot/
├── src/
│   ├── core/                 # Core chatbot logic
│   ├── rag/                  # RAG system components
│   ├── mcp/                  # MCP integration
│   ├── web/                  # Streamlit web interface
│   ├── api/                  # FastAPI backend
│   └── cli/                  # Command line interface
├── config/                   # Configuration files
├── data/                     # Knowledge base data
├── scripts/                  # Utility scripts
├── tests/                    # Test suite
├── docker/                   # Docker configurations
└── docs/                     # Documentation
```

## 🚀 Usage Examples

### CLI Usage
```bash
python -m src.cli --query "Doanh thu tuần trước là bao nhiêu?"
```

### Python API
```python
from src.core.chatbot import KiotVietRAGChatbot

chatbot = KiotVietRAGChatbot()
response = await chatbot.chat("Lấy danh sách sản phẩm bán chạy")
```

### REST API
```bash
curl -X POST "http://localhost:8080/chat" \
  -H "Content-Type: application/json" \
  -d '{"query": "Kiểm tra tồn kho sản phẩm"}'
```

## 📚 Documentation

- [Setup Guide](docs/setup.md)
- [Configuration](docs/configuration.md)
- [API Reference](docs/api.md)
- [Deployment](docs/deployment.md)

## 🧪 Testing

```bash
# Run all tests
pytest

# Run specific test suite
pytest tests/test_rag.py
pytest tests/test_mcp.py
```

## 🐳 Docker Deployment

```bash
# Build and run
docker-compose up -d

# Access services
# Web UI: http://localhost:8501
# API: http://localhost:8080
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [mcp-use](https://github.com/mcp-use/mcp-use) - MCP client framework
- [FastMCP](https://github.com/jlowin/fastmcp) - MCP server framework
- [LangChain](https://github.com/langchain-ai/langchain) - LLM framework
