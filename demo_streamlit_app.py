#!/usr/bin/env python3
"""
Demo: Streamlit Web Interface for RAG + MCP Chatbot
"""

import streamlit as st
import asyncio
import os
from dotenv import load_dotenv
import time
from demo_rag_mcp_chatbot import RAGMCPChatbot

# Load environment
load_dotenv('.env_demo')

# Page config
st.set_page_config(
    page_title="KiotViet RAG + MCP Chatbot",
    page_icon="🏪",
    layout="wide"
)

# Custom CSS
st.markdown("""
<style>
.main-header {
    text-align: center;
    color: #1f77b4;
    margin-bottom: 2rem;
}
.chat-message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}
.user-message {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}
.bot-message {
    background-color: #f1f8e9;
    border-left: 4px solid #4caf50;
}
.status-info {
    background-color: #fff3e0;
    padding: 0.5rem;
    border-radius: 0.25rem;
    border-left: 3px solid #ff9800;
    margin: 0.5rem 0;
}
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'chatbot' not in st.session_state:
    st.session_state.chatbot = None
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'is_initialized' not in st.session_state:
    st.session_state.is_initialized = False

@st.cache_resource
def initialize_chatbot():
    """Initialize the RAG MCP chatbot."""
    try:
        chatbot = RAGMCPChatbot()
        return chatbot, True
    except Exception as e:
        return None, str(e)

def main():
    """Main Streamlit app."""
    
    # Header
    st.markdown('<h1 class="main-header">🏪 KiotViet RAG + MCP Chatbot</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # MCP Server Status
        st.subheader("🔗 MCP Server Status")
        mcp_url = os.getenv("KIOTVIET_MCP_URL", "http://localhost:8000/mcp/")
        st.code(mcp_url)
        
        # Check server status
        if st.button("🔍 Check Server Status"):
            with st.spinner("Checking server..."):
                try:
                    import requests
                    health_url = os.getenv("KIOTVIET_MCP_HEALTH_URL", "http://localhost:8000/health")
                    response = requests.get(health_url, timeout=5)
                    if response.status_code == 200:
                        st.success("✅ Server is running!")
                        st.json(response.json())
                    else:
                        st.error(f"❌ Server error: {response.status_code}")
                except Exception as e:
                    st.error(f"❌ Cannot connect to server: {e}")
        
        st.markdown("---")
        
        # Settings
        st.subheader("🎛️ Chat Settings")
        max_steps = st.slider("Max Steps", 5, 30, 15)
        use_streaming = st.checkbox("Enable Streaming", value=False)
        show_debug = st.checkbox("Show Debug Info", value=False)
        
        st.markdown("---")
        
        # Sample queries
        st.subheader("💡 Sample Queries")
        sample_queries = [
            "Doanh thu là gì?",
            "Lấy doanh thu tuần trước",
            "Kiểm tra tồn kho sản phẩm",
            "Danh sách khách hàng VIP",
            "Phân tích theo chi nhánh"
        ]
        
        for query in sample_queries:
            if st.button(f"📝 {query}", key=f"sample_{hash(query)}"):
                st.session_state.sample_query = query
    
    # Main chat interface
    col1, col2 = st.columns([3, 1])
    
    with col1:
        st.subheader("💬 Chat Interface")
        
        # Initialize chatbot
        if not st.session_state.is_initialized:
            with st.spinner("🚀 Initializing RAG + MCP Chatbot..."):
                chatbot, error = initialize_chatbot()
                if chatbot:
                    st.session_state.chatbot = chatbot
                    st.session_state.is_initialized = True
                    st.success("✅ Chatbot initialized successfully!")
                else:
                    st.error(f"❌ Failed to initialize chatbot: {error}")
                    st.stop()
        
        # Chat input
        query_input = st.text_input(
            "Ask me anything about KiotViet:",
            value=st.session_state.get('sample_query', ''),
            key="chat_input"
        )
        
        # Clear sample query after use
        if 'sample_query' in st.session_state:
            del st.session_state.sample_query
        
        col_send, col_clear = st.columns([1, 1])
        
        with col_send:
            send_button = st.button("🚀 Send", type="primary")
        
        with col_clear:
            if st.button("🗑️ Clear History"):
                st.session_state.chat_history = []
                st.rerun()
        
        # Process query
        if send_button and query_input and st.session_state.chatbot:
            # Add user message to history
            st.session_state.chat_history.append({
                'type': 'user',
                'content': query_input,
                'timestamp': time.time()
            })
            
            # Process with chatbot
            with st.spinner("🤖 Processing your query..."):
                try:
                    if use_streaming:
                        # Streaming response
                        response_placeholder = st.empty()
                        full_response = ""
                        
                        async def stream_response():
                            nonlocal full_response
                            async for chunk in st.session_state.chatbot.streaming_chat(query_input):
                                full_response += chunk
                                response_placeholder.markdown(f"🤖 **Bot:** {full_response}")
                        
                        # Run streaming
                        asyncio.run(stream_response())
                        response = full_response
                    else:
                        # Regular response
                        response = asyncio.run(st.session_state.chatbot.chat(query_input))
                    
                    # Add bot response to history
                    st.session_state.chat_history.append({
                        'type': 'bot',
                        'content': response,
                        'timestamp': time.time()
                    })
                    
                except Exception as e:
                    st.error(f"❌ Error: {e}")
                    if show_debug:
                        st.exception(e)
            
            # Clear input and rerun
            st.rerun()
        
        # Display chat history
        st.subheader("📜 Chat History")
        
        if st.session_state.chat_history:
            for i, message in enumerate(reversed(st.session_state.chat_history)):
                if message['type'] == 'user':
                    st.markdown(f"""
                    <div class="chat-message user-message">
                        <strong>👤 You:</strong><br>
                        {message['content']}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="chat-message bot-message">
                        <strong>🤖 Bot:</strong><br>
                        {message['content']}
                    </div>
                    """, unsafe_allow_html=True)
        else:
            st.info("💡 Start a conversation by typing a question above!")
    
    with col2:
        st.subheader("📊 System Info")
        
        # System status
        if st.session_state.is_initialized:
            st.success("🟢 System Ready")
        else:
            st.warning("🟡 Initializing...")
        
        # Chat statistics
        if st.session_state.chat_history:
            user_messages = len([m for m in st.session_state.chat_history if m['type'] == 'user'])
            bot_messages = len([m for m in st.session_state.chat_history if m['type'] == 'bot'])
            
            st.metric("💬 User Messages", user_messages)
            st.metric("🤖 Bot Responses", bot_messages)
        
        # Debug info
        if show_debug and st.session_state.chatbot:
            st.subheader("🔍 Debug Info")
            
            # Conversation history from chatbot
            if hasattr(st.session_state.chatbot, 'conversation_history'):
                history_count = len(st.session_state.chatbot.conversation_history)
                st.metric("🧠 Internal History", history_count)
            
            # MCP client info
            st.text("MCP Client Status: Active")
            st.text(f"Vector DB: {os.getenv('VECTOR_DB_PATH', './demo_vectordb')}")

if __name__ == "__main__":
    main()
