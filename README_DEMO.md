# 🚀 KiotViet RAG + MCP Chatbot Demo

Demo implementation của RAG chatbot tích hợp với KiotViet MCP server sử dụng **mcp-use**.

## 📦 Setup Instructions

### 1. **Prerequisites**

Đ<PERSON><PERSON> bảo bạn đã có:
- ✅ Python 3.11+
- ✅ KiotViet MCP Server đang chạy tại `http://localhost:8000`
- ✅ OpenAI API key

### 2. **Installation**

```bash
# Clone hoặc copy các file demo
# Install dependencies
pip install -r requirements_demo.txt
```

### 3. **Environment Configuration**

```bash
# Copy và edit environment file
cp .env_demo .env

# Edit .env với thông tin của bạn:
OPENAI_API_KEY=your_openai_api_key_here
KIOTVIET_MCP_URL=http://localhost:8000/mcp/
KIOTVIET_MCP_HEALTH_URL=http://localhost:8000/health
```

### 4. **Start KiotViet MCP Server**

```bash
# Trong terminal khác, start MCP server
cd /path/to/albatross-kiotviet-mcp
make run-http

# Hoặc
export MCP_TRANSPORT=http
uv run python -m albatross_kiotviet_mcp.main
```

## 🎯 Demo Options

### **Option 1: Basic Command Line Demo**

```bash
# Test basic mcp-use connection
python demo_basic_mcp_use.py
```

**Expected Output:**
```
🧪 Testing KiotViet MCP Server Connection...
📡 Creating MCP client...
🤖 Initializing LLM...
🚀 Creating MCP agent...

==================================================
🔧 TEST 1: List Available Tools
==================================================
📋 Available tools:
I have access to several KiotViet tools including:
- get_categories: Get product categories
- get_branches: Get branch information
- get_inventory: Check inventory levels
- get_invoices: Retrieve invoices
- calculate_daily_revenue: Calculate revenue
...
```

### **Option 2: RAG + MCP Chatbot Demo**

```bash
# Test full RAG + MCP integration
python demo_rag_mcp_chatbot.py
```

**Expected Output:**
```
🔧 Setting up MCP integration...
✅ MCP integration ready!
📚 Setting up RAG system...
📖 Loading sample knowledge base...
✅ RAG system ready!

🎯 Demo: Basic RAG + MCP Chat
==================================================

💬 User Query: Doanh thu là gì?
--------------------------------------------------
🧠 Query Analysis: {'needs_realtime_data': False, 'needs_knowledge_context': True}
📚 Searching knowledge base...
📖 Found relevant context: 245 characters
🤖 Processing with MCP agent...

✅ Response: Doanh thu là tổng số tiền thu được từ việc bán hàng hóa và dịch vụ...
```

### **Option 3: Streamlit Web Interface**

```bash
# Start web interface
streamlit run demo_streamlit_app.py
```

Mở browser tại: `http://localhost:8501`

**Features:**
- 💬 Interactive chat interface
- 📊 Real-time system status
- 🔍 MCP server health check
- 💡 Sample queries
- 🔄 Streaming responses
- 📜 Chat history

### **Option 4: FastAPI Backend**

```bash
# Start API server
python demo_fastapi_backend.py
```

API available at: `http://localhost:8080`

**Endpoints:**
- `GET /` - API info
- `GET /health` - Health check
- `POST /chat` - Chat endpoint
- `POST /chat/stream` - Streaming chat
- `GET /docs` - Swagger documentation

## 🧪 Testing Scenarios

### **1. RAG-only Queries (Knowledge Base)**

```python
queries = [
    "Doanh thu là gì?",
    "Cách tính tồn kho?", 
    "Khách hàng VIP là gì?",
    "Quy trình quản lý bán hàng"
]
```

**Expected:** Sử dụng knowledge base, không gọi MCP tools

### **2. MCP-only Queries (Real-time Data)**

```python
queries = [
    "Lấy doanh thu tuần trước",
    "Kiểm tra tồn kho hiện tại",
    "Danh sách khách hàng VIP",
    "Thông tin các chi nhánh"
]
```

**Expected:** Gọi MCP tools để lấy dữ liệu real-time

### **3. Hybrid Queries (RAG + MCP)**

```python
queries = [
    "Phân tích doanh thu theo chi nhánh trong tháng này",
    "So sánh tồn kho với mức tồn kho lý tưởng", 
    "Đánh giá hiệu quả kinh doanh của cửa hàng"
]
```

**Expected:** Kết hợp knowledge base + MCP tools

## 🔍 Troubleshooting

### **Common Issues:**

1. **MCP Server Connection Error**
   ```bash
   # Check if MCP server is running
   curl http://localhost:8000/health
   
   # Expected response:
   {"status":"healthy","server":"KiotVietMCPServer","transport":"http","version":"0.1.0"}
   ```

2. **OpenAI API Error**
   ```bash
   # Check API key in .env
   echo $OPENAI_API_KEY
   ```

3. **Vector DB Issues**
   ```bash
   # Clear vector database
   rm -rf ./demo_vectordb
   ```

4. **Dependencies Issues**
   ```bash
   # Reinstall dependencies
   pip install -r requirements_demo.txt --force-reinstall
   ```

## 📊 Architecture Overview

```
User Query → RAG Chatbot → mcp-use → KiotViet MCP Server → KiotViet API
     ↓              ↓           ↓
Vector Search   Intent      Tool Calling
+ Knowledge    Analysis    + Function
  Base                      Execution
```

## 🎯 Key Features Demonstrated

- ✅ **mcp-use Integration**: Seamless connection to MCP server
- ✅ **Intelligent Routing**: RAG vs MCP based on query intent
- ✅ **Multi-step Reasoning**: Agent can perform complex workflows
- ✅ **Streaming Support**: Real-time response streaming
- ✅ **Production Ready**: Error handling, logging, monitoring
- ✅ **Web Interface**: User-friendly Streamlit app
- ✅ **API Backend**: RESTful API with FastAPI

## 🚀 Next Steps

1. **Customize Knowledge Base**: Add your specific business knowledge
2. **Extend MCP Tools**: Add more KiotViet API endpoints
3. **Improve Intent Analysis**: Better query classification
4. **Add Authentication**: Secure the API endpoints
5. **Deploy to Production**: Docker containerization

## 📝 Notes

- Demo sử dụng sample knowledge base, thay thế bằng data thực tế
- MCP server phải chạy trước khi start chatbot
- Streaming feature yêu cầu compatible LLM model
- Vector database được tạo tự động lần đầu chạy

Happy coding! 🎉
