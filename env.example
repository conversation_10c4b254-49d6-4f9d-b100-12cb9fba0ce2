# Required KiotViet API credentials
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_name_here

# Optional KiotViet configuration (with defaults)
KIOTVIET_AUTH_URL=https://id.kiotviet.vn/connect/token
KIOTVIET_API_BASE_URL=https://public.kiotapi.com
KIOTVIET_TOKEN_BUFFER_SECONDS=300
KIOTVIET_REQUEST_TIMEOUT=30
KIOTVIET_MAX_RETRIES=3
KIOTVIET_RETRY_DELAY=1

# MCP Transport configuration
# Use "http" for http-streamable protocol or "stdio" for traditional mode
MCP_TRANSPORT=http
MCP_HOST=0.0.0.0
MCP_PORT=8000
MCP_PATH=/mcp/

# Logging configuration
LOG_LEVEL=info