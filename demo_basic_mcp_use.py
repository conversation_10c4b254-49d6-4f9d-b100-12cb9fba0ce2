#!/usr/bin/env python3
"""
Demo: Basic mcp-use integration with KiotViet MCP Server
"""

import asyncio
import os
from dotenv import load_dotenv
from mcp_use import MCPAgent, MCPClient
from langchain_openai import ChatOpenAI

# Load environment variables
load_dotenv('.env_demo')

async def test_kiotviet_mcp_connection():
    """Test basic connection to KiotViet MCP server."""
    print("🧪 Testing KiotViet MCP Server Connection...")
    
    # MCP Server configuration
    config = {
        "mcpServers": {
            "kiotviet": {
                "url": os.getenv("KIOTVIET_MCP_URL", "http://localhost:8000/mcp/")
            }
        }
    }
    
    try:
        # Create MCP client
        print("📡 Creating MCP client...")
        client = MCPClient.from_dict(config)
        
        # Create LLM
        print("🤖 Initializing LLM...")
        llm = ChatOpenAI(
            model="gpt-4o",
            temperature=0.1,
            api_key=os.getenv("OPENAI_API_KEY")
        )
        
        # Create MCP agent
        print("🚀 Creating MCP agent...")
        agent = MCPAgent(
            llm=llm, 
            client=client,
            max_steps=10,
            verbose=True
        )
        
        # Test 1: List available tools
        print("\n" + "="*50)
        print("🔧 TEST 1: List Available Tools")
        print("="*50)
        
        result = await agent.run("What tools do you have available?")
        print(f"📋 Available tools:\n{result}")
        
        # Test 2: Get categories
        print("\n" + "="*50)
        print("🏪 TEST 2: Get KiotViet Categories")
        print("="*50)
        
        result = await agent.run("Get product categories from KiotViet")
        print(f"📦 Categories result:\n{result}")
        
        # Test 3: Get branches
        print("\n" + "="*50)
        print("🏢 TEST 3: Get KiotViet Branches")
        print("="*50)
        
        result = await agent.run("Get all branches from KiotViet")
        print(f"🏢 Branches result:\n{result}")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        try:
            await client.close_all_sessions()
        except:
            pass

async def demo_business_queries():
    """Demo business-specific queries."""
    print("\n🎯 Demo: Business Queries with KiotViet MCP")
    
    config = {
        "mcpServers": {
            "kiotviet": {
                "url": os.getenv("KIOTVIET_MCP_URL", "http://localhost:8000/mcp/")
            }
        }
    }
    
    try:
        client = MCPClient.from_dict(config)
        llm = ChatOpenAI(model="gpt-4o", temperature=0.1)
        agent = MCPAgent(llm=llm, client=client, max_steps=15)
        
        # Business queries
        queries = [
            "Tính doanh thu tuần trước",
            "Lấy danh sách sản phẩm bán chạy nhất",
            "Kiểm tra tồn kho của các sản phẩm",
            "Lấy thông tin khách hàng VIP",
            "Phân tích doanh thu theo chi nhánh"
        ]
        
        for i, query in enumerate(queries, 1):
            print(f"\n📊 Query {i}: {query}")
            print("-" * 40)
            
            try:
                result = await agent.run(query)
                print(f"💡 Result: {result}")
            except Exception as e:
                print(f"❌ Error for query '{query}': {e}")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
    finally:
        try:
            await client.close_all_sessions()
        except:
            pass

if __name__ == "__main__":
    print("🚀 KiotViet MCP + mcp-use Demo")
    print("=" * 50)
    
    # Run basic connection test
    asyncio.run(test_kiotviet_mcp_connection())
    
    # Run business queries demo
    asyncio.run(demo_business_queries())
