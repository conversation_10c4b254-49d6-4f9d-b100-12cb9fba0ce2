#!/usr/bin/env python3
"""
Demo: RAG Chatbot with mcp-use + KiotViet MCP Server
"""

import asyncio
import os
from typing import List, Dict, Any
from dotenv import load_dotenv

# MCP and LLM imports
from mcp_use import MCPAgent, MCPClient
from langchain_openai import Chat<PERSON>penAI, OpenAIEmbeddings

# RAG imports
from langchain.vectorstores import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

# Load environment
load_dotenv('.env_demo')

class RAGMCPChatbot:
    """RAG Chatbot integrated with KiotViet MCP Server via mcp-use."""
    
    def __init__(self):
        self.setup_mcp()
        self.setup_rag()
        self.conversation_history = []
    
    def setup_mcp(self):
        """Setup MCP client and agent."""
        print("🔧 Setting up MCP integration...")
        
        # MCP configuration
        self.mcp_config = {
            "mcpServers": {
                "kiotviet": {
                    "url": os.getenv("KIOTVIET_MCP_URL", "http://localhost:8000/mcp/")
                }
            }
        }
        
        # Create MCP client
        self.mcp_client = MCPClient.from_dict(self.mcp_config)
        
        # Create LLM
        self.llm = ChatOpenAI(
            model="gpt-4o",
            temperature=0.1,
            api_key=os.getenv("OPENAI_API_KEY")
        )
        
        # Create MCP agent
        self.mcp_agent = MCPAgent(
            llm=self.llm,
            client=self.mcp_client,
            max_steps=15,
            verbose=True
        )
        
        print("✅ MCP integration ready!")
    
    def setup_rag(self):
        """Setup RAG components."""
        print("📚 Setting up RAG system...")
        
        # Embeddings
        self.embeddings = OpenAIEmbeddings(
            api_key=os.getenv("OPENAI_API_KEY")
        )
        
        # Vector store
        persist_directory = os.getenv("VECTOR_DB_PATH", "./demo_vectordb")
        self.vectorstore = Chroma(
            embedding_function=self.embeddings,
            persist_directory=persist_directory
        )
        
        # Text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=int(os.getenv("CHUNK_SIZE", 1000)),
            chunk_overlap=int(os.getenv("CHUNK_OVERLAP", 200))
        )
        
        # Load sample knowledge base
        self.load_sample_knowledge()
        
        print("✅ RAG system ready!")
    
    def load_sample_knowledge(self):
        """Load sample business knowledge into vector store."""
        print("📖 Loading sample knowledge base...")
        
        # Sample business documents
        sample_docs = [
            """
            KiotViet là hệ thống quản lý bán hàng toàn diện cho các cửa hàng bán lẻ.
            Hệ thống cung cấp các tính năng quản lý sản phẩm, đơn hàng, khách hàng, 
            tồn kho và báo cáo doanh thu chi tiết.
            """,
            """
            Doanh thu là tổng số tiền thu được từ việc bán hàng hóa và dịch vụ.
            Để tính doanh thu, cần tổng hợp tất cả các hóa đơn bán hàng trong khoảng thời gian nhất định.
            Doanh thu có thể được phân tích theo ngày, tuần, tháng, quý hoặc năm.
            """,
            """
            Quản lý tồn kho là việc theo dõi số lượng hàng hóa có sẵn trong kho.
            Tồn kho cần được cập nhật thường xuyên để tránh tình trạng hết hàng hoặc tồn kho thừa.
            Các chỉ số quan trọng bao gồm: tồn kho đầu kỳ, nhập kho, xuất kho, tồn kho cuối kỳ.
            """,
            """
            Khách hàng VIP là những khách hàng có giá trị mua hàng cao và thường xuyên.
            Việc phân loại khách hàng giúp cửa hàng có chiến lược chăm sóc và ưu đãi phù hợp.
            Tiêu chí đánh giá: tổng giá trị mua hàng, tần suất mua hàng, thời gian là khách hàng.
            """,
            """
            Báo cáo doanh thu theo chi nhánh giúp so sánh hiệu quả kinh doanh giữa các cửa hàng.
            Các chỉ số cần theo dõi: doanh thu, lợi nhuận, số lượng đơn hàng, khách hàng mới.
            Phân tích này giúp xác định chi nhánh hoạt động tốt và cần cải thiện.
            """
        ]
        
        # Convert to documents
        documents = []
        for i, content in enumerate(sample_docs):
            doc = Document(
                page_content=content.strip(),
                metadata={"source": f"business_knowledge_{i+1}"}
            )
            documents.append(doc)
        
        # Split and add to vector store
        splits = self.text_splitter.split_documents(documents)
        
        # Check if vector store is empty
        try:
            existing_docs = self.vectorstore.get()
            if not existing_docs['ids']:
                self.vectorstore.add_documents(splits)
                print(f"📚 Added {len(splits)} document chunks to knowledge base")
            else:
                print(f"📚 Knowledge base already contains {len(existing_docs['ids'])} documents")
        except:
            # If get() fails, assume empty and add documents
            self.vectorstore.add_documents(splits)
            print(f"📚 Added {len(splits)} document chunks to knowledge base")
    
    async def rag_search(self, query: str, k: int = 3) -> str:
        """Perform RAG search for relevant context."""
        try:
            docs = self.vectorstore.similarity_search(query, k=k)
            context = "\n\n".join([doc.page_content for doc in docs])
            return context
        except Exception as e:
            print(f"⚠️ RAG search error: {e}")
            return ""
    
    def analyze_query_intent(self, query: str) -> Dict[str, bool]:
        """Analyze query to determine if it needs RAG context or MCP tools."""
        query_lower = query.lower()
        
        # Keywords that suggest need for real-time data (MCP tools)
        realtime_keywords = [
            'doanh thu', 'revenue', 'bán hàng', 'sales',
            'tồn kho', 'inventory', 'stock',
            'khách hàng', 'customer', 'đơn hàng', 'order',
            'chi nhánh', 'branch', 'cửa hàng', 'store',
            'hóa đơn', 'invoice', 'thanh toán', 'payment'
        ]
        
        # Keywords that suggest need for knowledge base (RAG)
        knowledge_keywords = [
            'là gì', 'what is', 'giải thích', 'explain',
            'cách tính', 'how to calculate', 'định nghĩa', 'definition',
            'hướng dẫn', 'guide', 'quy trình', 'process'
        ]
        
        needs_realtime = any(keyword in query_lower for keyword in realtime_keywords)
        needs_knowledge = any(keyword in query_lower for keyword in knowledge_keywords)
        
        return {
            'needs_realtime_data': needs_realtime,
            'needs_knowledge_context': needs_knowledge or not needs_realtime,
            'is_complex_query': len(query.split()) > 5
        }
    
    async def chat(self, query: str) -> str:
        """Main chat method combining RAG and MCP."""
        print(f"\n💬 User Query: {query}")
        print("-" * 50)
        
        try:
            # Analyze query intent
            intent = self.analyze_query_intent(query)
            print(f"🧠 Query Analysis: {intent}")
            
            # Get RAG context if needed
            context = ""
            if intent['needs_knowledge_context']:
                print("📚 Searching knowledge base...")
                context = await self.rag_search(query)
                if context:
                    print(f"📖 Found relevant context: {len(context)} characters")
            
            # Prepare enhanced query
            if context:
                enhanced_query = f"""
Kiến thức nền tảng:
{context}

Câu hỏi của người dùng: {query}

Hãy sử dụng kiến thức nền tảng và các công cụ có sẵn để trả lời câu hỏi một cách đầy đủ và chính xác.
Nếu cần dữ liệu thời gian thực, hãy sử dụng các công cụ KiotViet để lấy thông tin mới nhất.
"""
            else:
                enhanced_query = query
            
            # Use MCP agent for tool calling and response generation
            print("🤖 Processing with MCP agent...")
            result = await self.mcp_agent.run(enhanced_query)
            
            # Store in conversation history
            self.conversation_history.append({
                'query': query,
                'response': result,
                'used_context': bool(context),
                'intent': intent
            })
            
            return result
            
        except Exception as e:
            error_msg = f"❌ Error processing query: {e}"
            print(error_msg)
            return error_msg
    
    async def streaming_chat(self, query: str):
        """Streaming version of chat method."""
        print(f"\n💬 Streaming Query: {query}")
        print("-" * 50)
        
        try:
            # Quick intent analysis and context retrieval
            intent = self.analyze_query_intent(query)
            context = ""
            
            if intent['needs_knowledge_context']:
                context = await self.rag_search(query)
            
            # Prepare enhanced query
            enhanced_query = f"""
Kiến thức nền tảng:
{context}

Câu hỏi: {query}

Trả lời dựa trên kiến thức và sử dụng công cụ nếu cần.
""" if context else query
            
            # Stream response
            print("🔄 Streaming response...")
            async for chunk in self.mcp_agent.astream(enhanced_query):
                if 'messages' in chunk:
                    yield chunk['messages']
                    
        except Exception as e:
            yield f"❌ Streaming error: {e}"
    
    async def cleanup(self):
        """Clean up resources."""
        try:
            await self.mcp_client.close_all_sessions()
            print("🧹 Cleanup completed")
        except:
            pass

# Demo functions
async def demo_basic_chat():
    """Demo basic RAG + MCP chat functionality."""
    print("🎯 Demo: Basic RAG + MCP Chat")
    print("=" * 50)
    
    chatbot = RAGMCPChatbot()
    
    try:
        # Test queries
        queries = [
            "Doanh thu là gì?",  # Should use RAG
            "Lấy doanh thu tuần trước",  # Should use MCP tools
            "Cách tính tồn kho?",  # Should use RAG
            "Kiểm tra tồn kho hiện tại",  # Should use MCP tools
            "Phân tích doanh thu theo chi nhánh trong tháng này"  # Should use both
        ]
        
        for query in queries:
            result = await chatbot.chat(query)
            print(f"\n✅ Response: {result}")
            print("\n" + "="*50)
            
    finally:
        await chatbot.cleanup()

async def demo_streaming_chat():
    """Demo streaming chat functionality."""
    print("🎯 Demo: Streaming RAG + MCP Chat")
    print("=" * 50)
    
    chatbot = RAGMCPChatbot()
    
    try:
        query = "Phân tích doanh thu và tồn kho của cửa hàng trong tuần qua"
        
        print(f"Query: {query}")
        print("Streaming response:")
        print("-" * 30)
        
        async for chunk in chatbot.streaming_chat(query):
            print(chunk, end="", flush=True)
        
        print("\n" + "="*50)
        
    finally:
        await chatbot.cleanup()

if __name__ == "__main__":
    print("🚀 RAG + MCP Chatbot Demo")
    print("=" * 50)
    
    # Run demos
    asyncio.run(demo_basic_chat())
    # asyncio.run(demo_streaming_chat())
