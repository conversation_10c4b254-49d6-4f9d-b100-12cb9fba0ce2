"""Main entry point for the KiotViet MCP Server."""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv
from loguru import logger

from .server import main as server_main

# Load environment variables
load_dotenv()

# Create logs directory if it doesn't exist
# Check if we're in a container (logs dir exists at /app/logs)
if os.path.exists('/app/logs'):
    log_dir = '/app/logs'
else:
    # Local development - use project root
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.join(current_dir, '..', '..', '..')
    log_dir = os.path.join(project_root, 'logs')
    os.makedirs(log_dir, exist_ok=True)

# Configure loguru logging
log_file = os.path.join(log_dir, f'kiotviet_mcp_{datetime.now().strftime("%Y%m%d")}.log')

# Remove default logger
logger.remove()

# Add console logger
logger.add(
    sys.stderr,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)

# Add file logger
logger.add(
    log_file,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG",
    rotation="1 day",
    retention="7 days"
)

print(f"📁 Log file: {log_file}")


def main() -> None:
    """Main entry point."""
    logger.info("Starting Albatross KiotViet MCP Server...")
    return server_main()


if __name__ == "__main__":
    main()
