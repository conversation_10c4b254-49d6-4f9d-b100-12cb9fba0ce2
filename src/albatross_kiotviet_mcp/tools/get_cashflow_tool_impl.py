"""Triển khai công cụ phiếu thu chi với các tùy chọn lọc toàn di<PERSON>n."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class CashFlowToolImpl(BaseTool):
    """Triển khai công cụ phiếu thu chi với khả năng lọc toàn di<PERSON>n."""
    
    async def execute(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_direction: str = "Asc",
        branchIds: Optional[List[int]] = None,
        code: Optional[List[str]] = None,
        userId: Optional[int] = None,
        accountId: Optional[int] = None,
        partnerType: Optional[str] = None,
        method: Optional[List[str]] = None,
        cashFlowGroupId: Optional[List[int]] = None,
        usedForFinancialReporting: Optional[int] = None,
        partnerName: Optional[str] = None,
        contactNumber: Optional[str] = None,
        isReceipt: Optional[bool] = None,
        includeAccount: Optional[bool] = None,
        includeBranch: Optional[bool] = None,
        includeUser: Optional[bool] = None,
        startDate: Optional[str] = None,
        endDate: Optional[str] = None,
        status: Optional[int] = None,
        ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ phiếu thu chi với các tùy chọn lọc theo API spec chuẩn.

        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định 20, tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp
            branchIds: ID chi nhánh
            code: Danh sách mã code của phiếu
            userId: Id người tạo
            accountId: Tài khoản nhận
            partnerType: Loại người nộp/nhận
            method: Danh sách phương thức thanh toán
            cashFlowGroupId: Loại thu/chi
            usedForFinancialReporting: Lọc theo kết qua kinh doanh
            partnerName: Tên người nộp/nhận
            contactNumber: Số điện thoại người nộp/nhận
            isReceipt: Theo phiếu thu/chi
            includeAccount: Lấy thông tin tài khoản ngân hàng hay không
            includeBranch: Lấy tên chi nhánh hay không
            includeUser: Lấy tên người tạo hay không
            startDate: Thời gian bắt đầu
            endDate: Thời gian kết thúc
            status: Trạng thái phiếu
            ids: Id phiếu thu/chi

        Returns:
            Dictionary chứa dữ liệu phiếu thu chi từ API

        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)

        # Thực hiện gọi API
        result = await self.api_client.get_cashflow(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            # Tham số chuẩn theo API spec
            branchIds=branchIds,
            code=code,
            userId=userId,
            accountId=accountId,
            partnerType=partnerType,
            method=method,
            cashFlowGroupId=cashFlowGroupId,
            usedForFinancialReporting=usedForFinancialReporting,
            partnerName=partnerName,
            contactNumber=contactNumber,
            isReceipt=isReceipt,
            includeAccount=includeAccount,
            includeBranch=includeBranch,
            includeUser=includeUser,
            startDate=startDate,
            endDate=endDate,
            status=status,
            ids=ids
        )
        
        logger.info(f"Đã lấy thành công {len(result.data)} phiếu thu chi trong tổng số {result.total}")
        
        # Chuyển đổi thành dictionary để đầu ra nhất quán
        return result.model_dump()