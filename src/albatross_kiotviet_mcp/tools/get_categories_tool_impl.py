"""Categories tool implementation using new architecture."""

from typing import Dict, Any
from .core.base_tool import BaseTool


class CategoriesToolImpl(BaseTool):
    """Implementation for categories tool."""
    
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Execute categories tool logic.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index for pagination
            order_direction: Sort order direction
            hierarchical_data: Whether to return hierarchical structure
            
        Returns:
            Dictionary containing category data from the API
            
        Raises:
            ValueError: If parameters are invalid
        """
        
        # Validate parameters using base class methods
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Make API call
        result = await self.api_client.get_categories(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            hierarchical_data=hierarchical_data
        )
        
        return result.model_dump()