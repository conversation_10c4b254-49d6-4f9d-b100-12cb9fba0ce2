"""Schema cho tham số công cụ khách hàng."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class CustomersParams(PaginationParams):
    """Tham số cho công cụ khách hàng với các tùy chọn lọc theo API chính thức KiotViet."""
    
    # Tìm kiếm theo mã khách hàng
    code: Optional[str] = Field(
        default=None,
        description="Tìm kiếm khách hàng theo mã khách hàng"
    )
    
    # Tìm kiếm theo tên khách hàng
    name: Optional[str] = Field(
        default=None,
        description="Tìm kiếm theo tên khách hàng"
    )
    
    # Tìm kiếm theo số điện thoại
    contact_number: Optional[str] = Field(
        default=None,
        description="Tìm kiếm theo số điện thoại khách hàng"
    )
    
    # Thời gian cập nhật
    last_modified_from: Optional[str] = Field(
        default=None,
        description="Thời gian cập nhật (định dạng YYYY-MM-DD hoặc ISO datetime)"
    )
    
    # Tùy chọn sắp xếp
    order_by: Optional[str] = Field(
        default=None,
        description="Sắp xếp dữ liệu theo trường (ví dụ: 'name', 'createdDate')"
    )
    
    # Tùy chọn bao gồm thông tin bổ sung
    include_remove_ids: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin danh sách ID bị xóa dựa trên lastModifiedFrom"
    )
    
    include_total: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin TotalInvoice, TotalPoint, TotalRevenue"
    )
    
    include_customer_group: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin nhóm khách hàng hay không"
    )
    
    include_customer_social: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin Psid facebook fanpage của khách hàng hay không"
    )
    
    # Lọc theo ngày sinh nhật
    birth_date: Optional[str] = Field(
        default=None,
        description="Lọc khách hàng theo ngày sinh nhật (định dạng YYYY-MM-DD)"
    )
    
    # Lọc theo nhóm khách hàng
    group_id: Optional[int] = Field(
        default=None,
        description="Lọc theo nhóm khách hàng"
    )