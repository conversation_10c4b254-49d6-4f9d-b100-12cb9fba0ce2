"""Schema cho tham số công cụ đơn hàng."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class OrdersParams(PaginationParams):
    """Tham số cho công cụ đơn hàng theo API spec chuẩn."""

    # Tham số chuẩn theo API spec
    branchIds: Optional[List[int]] = Field(
        default=None,
        description="ID chi nhánh (danh sách)"
    )

    customerIds: Optional[List[int]] = Field(
        default=None,
        description="ID khách hàng (danh sách)"
    )

    customerCode: Optional[str] = Field(
        default=None,
        description="Mã khách hàng"
    )

    status: Optional[List[int]] = Field(
        default=None,
        description="Tình trạng đặt hàng (danh sách)"
    )

    includePayment: Optional[bool] = Field(
        default=False,
        description="C<PERSON> lấy thông tin thanh toán"
    )

    includeOrderDelivery: Optional[bool] = Field(
        default=False,
        description="<PERSON><PERSON> lấy thông tin giao hàng"
    )

    lastModifiedFrom: Optional[str] = Field(
        default=None,
        description="Thời gian cập nhật từ (định dạng YYYY-MM-DD hoặc YYYY-MM-DDTHH:MM:SS)"
    )

    toDate: Optional[str] = Field(
        default=None,
        description="Thời gian cập nhật đến (định dạng YYYY-MM-DD hoặc YYYY-MM-DDTHH:MM:SS)"
    )

    orderBy: Optional[str] = Field(
        default=None,
        description="Sắp xếp dữ liệu theo trường (ví dụ: name, createdDate)"
    )

    createdDate: Optional[str] = Field(
        default=None,
        description="Thời gian tạo (định dạng YYYY-MM-DD hoặc YYYY-MM-DDTHH:MM:SS)"
    )

    saleChannelId: Optional[int] = Field(
        default=None,
        description="ID kênh bán hàng"
    )