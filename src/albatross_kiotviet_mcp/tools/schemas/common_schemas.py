"""Common schema definitions shared across tools."""

from pydantic import BaseModel, <PERSON>
from typing import Union
from datetime import datetime


class PaginationParams(BaseModel):
    """Common pagination parameters."""
    page_size: int = Field(
        default=50, 
        ge=1, 
        le=100, 
        description="Number of items per page (1-100)"
    )
    current_item: int = Field(
        default=0, 
        ge=0, 
        description="Starting item index for pagination"
    )
    order_direction: str = Field(
        default="Asc", 
        pattern="^(Asc|Desc)$", 
        description="Sort order: Asc or Desc"
    )


class DateRangeParams(BaseModel):
    """Common date range parameters."""
    from_date: Union[str, datetime] = Field(
        description="Start date (YYYY-MM-DD or ISO format)"
    )
    to_date: Union[str, datetime] = Field(
        description="End date (YYYY-MM-DD or ISO format)"
    )