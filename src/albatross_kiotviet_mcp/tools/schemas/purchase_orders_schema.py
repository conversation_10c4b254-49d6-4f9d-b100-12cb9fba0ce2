"""Schema cho tham số công cụ đơn nhập hàng."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class PurchaseOrdersParams(PaginationParams):
    """Tham số cho công cụ đơn nhập hàng theo API spec chuẩn."""

    # Tham số chuẩn theo API spec
    branchIds: Optional[List[int]] = Field(
        default=None,
        description="ID chi nhánh (danh sách)"
    )

    status: Optional[List[int]] = Field(
        default=None,
        description="Tình trạng đặt hàng (danh sách)"
    )

    includePayment: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin thanh toán"
    )

    includeOrderDelivery: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin giao hàng"
    )

    fromPurchaseDate: Optional[str] = Field(
        default=None,
        description="Từ ngày nhập hàng (định dạng YYYY-MM-DD)"
    )

    toPurchaseDate: Optional[str] = Field(
        default=None,
        description="Đến ngày nhập hàng (định dạng YYYY-MM-DD)"
    )