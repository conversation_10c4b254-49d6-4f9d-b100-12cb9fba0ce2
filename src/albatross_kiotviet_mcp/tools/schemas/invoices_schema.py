"""Schema cho tham số công cụ hóa đơn."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class InvoicesParams(PaginationParams):
    """Tham số cho công cụ hóa đơn với các tùy chọn lọc theo API chính thức KiotViet."""
    
    # Lọc chi nhánh
    branch_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID chi nhánh để lọc theo"
    )
    
    # Lọc khách hàng
    customer_ids: Optional[List[int]] = Field(
        default=None,
        description="Danh sách ID khách hàng để lọc theo"
    )
    
    # Lọc theo mã khách hàng
    customer_code: Optional[str] = Field(
        default=None,
        description="Mã khách hàng để lọc theo"
    )
    
    # Lọc trạng thái
    status: Optional[List[int]] = Field(
        default=None,
        description="Danh sách tình trạng hóa đơn để lọc theo"
    )
    
    # Tùy chọn bao gồm thông tin bổ sung
    include_payment: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin thanh toán hay không"
    )
    
    include_invoice_delivery: Optional[bool] = Field(
        default=False,
        description="Có lấy thông tin giao hàng hay không"
    )
    
    # Lọc theo thời gian
    last_modified_from: Optional[str] = Field(
        default=None,
        description="Thời gian cập nhật từ (định dạng YYYY-MM-DD hoặc ISO datetime)"
    )
    
    to_date: Optional[str] = Field(
        default=None,
        description="Thời gian cập nhật đến (định dạng YYYY-MM-DD hoặc ISO datetime)"
    )
    
    created_date: Optional[str] = Field(
        default=None,
        description="Thời gian tạo (định dạng YYYY-MM-DD hoặc ISO datetime)"
    )
    
    from_purchase_date: Optional[str] = Field(
        default=None,
        description="Từ ngày giao dịch (định dạng YYYY-MM-DD)"
    )
    
    to_purchase_date: Optional[str] = Field(
        default=None,
        description="Đến ngày giao dịch (định dạng YYYY-MM-DD)"
    )
    
    # Lọc theo đơn đặt hàng
    order_id: Optional[int] = Field(
        default=None,
        description="Lọc danh sách hóa đơn theo ID của đơn đặt hàng"
    )
    
    # Tùy chọn sắp xếp
    order_by: Optional[str] = Field(
        default=None,
        description="Sắp xếp dữ liệu theo trường (ví dụ: 'createdDate', 'total')"
    )