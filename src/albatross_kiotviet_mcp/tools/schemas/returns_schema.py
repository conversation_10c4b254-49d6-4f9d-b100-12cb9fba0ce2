"""Schema cho tham số công cụ đơn trả hàng."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class ReturnsParams(PaginationParams):
    """Tham số cho công cụ đơn trả hàng theo API spec chuẩn."""

    # Tham số chuẩn theo API spec
    orderBy: Optional[str] = Field(
        default=None,
        description="Sắp xếp dữ liệu theo trường orderBy (ví dụ: orderBy=Name)"
    )

    lastModifiedFrom: Optional[str] = Field(
        default=None,
        description="Th<PERSON><PERSON> gian cậ<PERSON> nh<PERSON> (định dạng datetime)"
    )

    fromReturnDate: Optional[str] = Field(
        default=None,
        description="Từ ngày trả hàng (định dạng YYYY-MM-DD)"
    )

    toReturnDate: Optional[str] = Field(
        default=None,
        description="Đến ngày trả hàng (định dạng YYYY-MM-DD)"
    )

    includePayment: Optional[bool] = Field(
        default=False,
        description="<PERSON><PERSON> lấy thông tin danh sách thanh toán"
    )