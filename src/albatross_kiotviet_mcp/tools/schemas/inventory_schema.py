"""Schema for inventory tool parameters."""

from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Union
from datetime import datetime


class InventoryParams(BaseModel):
    """Parameters for inventory tool."""
    page_size: int = Field(
        default=20, 
        ge=1, 
        le=100, 
        description="Number of items per page (1-100)"
    )
    current_item: int = Field(
        default=0, 
        ge=0, 
        description="Starting item index for pagination"
    )
    order_by: Optional[str] = Field(
        default=None, 
        description="Sort data by field (e.g., 'Code')"
    )
    last_modified_from: Optional[Union[str, datetime]] = Field(
        default=None, 
        description="Filter by last modified time (ISO format)"
    )
    branch_ids: Optional[List[int]] = Field(
        default=None, 
        description="List of branch IDs to filter by"
    )