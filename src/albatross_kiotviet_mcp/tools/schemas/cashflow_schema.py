"""Schema cho tham số công cụ phiếu thu chi."""

from typing import Optional, List
from pydantic import BaseModel, Field
from .common_schemas import PaginationParams


class CashFlowParams(PaginationParams):
    """Tham số cho công cụ phiếu thu chi theo API spec chuẩn."""

    # Tham số chuẩn theo API spec
    branchIds: Optional[List[int]] = Field(
        default=None,
        description="ID chi nhánh"
    )

    code: Optional[List[str]] = Field(
        default=None,
        description="Danh sách mã code của phiếu"
    )

    userId: Optional[int] = Field(
        default=None,
        description="Id người tạo"
    )

    accountId: Optional[int] = Field(
        default=None,
        description="Tài khoản nhận"
    )

    partnerType: Optional[str] = Field(
        default=None,
        description="Loại người nộp/nhận: A: tất cả, C: kh<PERSON>ch hàng, S: nhà cung cấp, U: nh<PERSON> viên, D: tối tác giao hàng, O: khác"
    )

    method: Optional[List[str]] = Field(
        default=None,
        description="Danh sách phương thức thanh toán"
    )

    cashFlowGroupId: Optional[List[int]] = Field(
        default=None,
        description="Loại thu/chi"
    )

    usedForFinancialReporting: Optional[int] = Field(
        default=None,
        description="Lọc theo kết qua kinh doanh: 0: không hoạch toán, 1: đưa vào hoạch toán"
    )

    partnerName: Optional[str] = Field(
        default=None,
        description="Tên người nộp/nhận"
    )

    contactNumber: Optional[str] = Field(
        default=None,
        description="Số điện thoại người nộp/nhận"
    )

    isReceipt: Optional[bool] = Field(
        default=None,
        description="Theo phiếu thu/chi; True: thu, false: chi"
    )

    includeAccount: Optional[bool] = Field(
        default=False,
        description="Lấy thông tin tài khoản ngân hàng hay không"
    )

    includeBranch: Optional[bool] = Field(
        default=False,
        description="Lấy tên chi nhánh hay không"
    )

    includeUser: Optional[bool] = Field(
        default=False,
        description="Lấy tên người tạo hay không"
    )

    startDate: Optional[str] = Field(
        default=None,
        description="Thời gian bắt đầu"
    )

    endDate: Optional[str] = Field(
        default=None,
        description="Thời gian kết thúc"
    )

    status: Optional[int] = Field(
        default=None,
        description="Trạng thái phiếu; 0: Đã thanh toán, 1: Đã hủy, không truyền: tất cả"
    )

    ids: Optional[List[int]] = Field(
        default=None,
        description="Id phiếu thu/chi"
    )