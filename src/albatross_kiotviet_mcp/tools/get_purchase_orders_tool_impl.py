"""Triển khai công cụ đơn nhập hàng với các tùy chọn lọc toàn diện."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class PurchaseOrdersToolImpl(BaseTool):
    """Triển khai công cụ đơn nhập hàng với khả năng lọc toàn diện."""
    
    async def execute(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_direction: str = "Asc",
        # Tham số chuẩn theo API spec
        branchIds: Optional[List[int]] = None,
        status: Optional[List[int]] = None,
        includePayment: Optional[bool] = None,
        includeOrderDelivery: Optional[bool] = None,
        fromPurchaseDate: Optional[str] = None,
        toPurchaseDate: Optional[str] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ đơn nhập hàng với các tùy chọn lọc theo API spec chuẩn.

        Args:
            page_size: <PERSON><PERSON> lượng mục trên mỗi trang (mặc định 20, tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp
            branchIds: ID chi nhánh (danh sách)
            status: Tình trạng đặt hàng (danh sách)
            includePayment: Có lấy thông tin thanh toán
            includeOrderDelivery: Có lấy thông tin giao hàng
            fromPurchaseDate: Từ ngày nhập hàng
            toPurchaseDate: Đến ngày nhập hàng

        Returns:
            Dictionary chứa dữ liệu đơn nhập hàng từ API

        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        
        # Xác thực tham số bằng phương thức lớp cơ sở
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Thực hiện gọi API
        result = await self.api_client.get_purchase_orders(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            # Tham số chuẩn theo API spec
            branchIds=branchIds,
            status=status,
            includePayment=includePayment,
            includeOrderDelivery=includeOrderDelivery,
            fromPurchaseDate=fromPurchaseDate,
            toPurchaseDate=toPurchaseDate
        )
        
        return result.model_dump()