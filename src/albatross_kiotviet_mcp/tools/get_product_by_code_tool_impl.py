"""Get product by code tool implementation."""

from typing import Dict, Any
from loguru import logger

from ..api.api_client import KiotVietAPIClient
from .core.base_tool import BaseTool


class ProductByCodeToolImpl(BaseTool):
    """Tool implementation for retrieving product details by product code."""

    async def execute(self, code: str) -> Dict[str, Any]:
        """Execute the get product by code operation.
        
        Args:
            code: Product code to search for
            
        Returns:
            Dictionary containing product details
            
        Raises:
            ValueError: If product code is invalid
            Exception: For API errors or product not found
        """
        if not code or not code.strip():
            raise ValueError("Product code cannot be empty")
        
        try:
            # Get product from API
            product = await self.api_client.get_product_by_code(code.strip())
            
            # Convert to dictionary for MCP response
            result = {
                "success": True,
                "product": product.model_dump(),
                "message": f"Successfully retrieved product with code: {code}"
            }
            
            logger.info(f"Successfully retrieved product: {product.name} (ID: {product.id})")
            return result
            
        except Exception as e:
            logger.error(f"Failed to retrieve product with code {code}: {str(e)}")
            
            # Handle specific error cases
            if "404" in str(e):
                return {
                    "success": False,
                    "product": None,
                    "error": f"Product with code '{code}' not found",
                    "error_type": "not_found"
                }
            
            # Re-raise for middleware to handle
            raise