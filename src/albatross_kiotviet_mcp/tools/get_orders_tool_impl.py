"""Triển khai công cụ đơn hàng với các tùy chọn lọc toàn diện."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class OrdersToolImpl(BaseTool):
    """Triển khai công cụ đơn hàng với khả năng lọc toàn diện."""
    
    async def execute(
        self,
        page_size: int = 20,
        current_item: int = 0,
        order_direction: str = "Asc",
        # Tham số chuẩn theo API spec
        branchIds: Optional[List[int]] = None,
        customerIds: Optional[List[int]] = None,
        customerCode: Optional[str] = None,
        status: Optional[List[int]] = None,
        includePayment: Optional[bool] = None,
        includeOrderDelivery: Optional[bool] = None,
        lastModifiedFrom: Optional[str] = None,
        toDate: Optional[str] = None,
        orderBy: Optional[str] = None,
        createdDate: Optional[str] = None,
        saleChannelId: Optional[int] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ đơn hàng với các tùy chọn lọc theo API spec chuẩn.

        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định 20, tối đa 100)
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp (Asc hoặc Desc)
            branchIds: ID chi nhánh (danh sách)
            customerIds: ID khách hàng (danh sách)
            customerCode: Mã khách hàng
            status: Tình trạng đặt hàng (danh sách)
            includePayment: Có lấy thông tin thanh toán
            includeOrderDelivery: Có lấy thông tin giao hàng
            lastModifiedFrom: Thời gian cập nhật từ
            toDate: Thời gian cập nhật đến
            orderBy: Sắp xếp dữ liệu theo trường
            createdDate: Thời gian tạo
            saleChannelId: ID kênh bán hàng

        Returns:
            Dictionary chứa dữ liệu đơn hàng từ API

        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        
        # Xác thực tham số bằng phương thức lớp cơ sở
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Thực hiện gọi API
        result = await self.api_client.get_orders(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            # Tham số chuẩn theo API spec
            branchIds=branchIds,
            customerIds=customerIds,
            customerCode=customerCode,
            status=status,
            includePayment=includePayment,
            includeOrderDelivery=includeOrderDelivery,
            lastModifiedFrom=lastModifiedFrom,
            toDate=toDate,
            orderBy=orderBy,
            createdDate=createdDate,
            saleChannelId=saleChannelId
        )
        
        return result.model_dump()