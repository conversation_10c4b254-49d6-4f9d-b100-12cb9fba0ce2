"""Daily revenue calculation tool implementation using new architecture."""

from typing import Dict, Any, Union
from datetime import datetime
from .core.base_tool import BaseTool


class DailyRevenueToolImpl(BaseTool):
    """Implementation for daily revenue calculation tool."""
    
    async def execute(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        include_details: bool = False
    ) -> Dict[str, Any]:
        """Execute daily revenue calculation tool logic.
        
        Args:
            from_date: Start date (datetime object or ISO string like '2025-01-15')
            to_date: End date (datetime object or ISO string like '2025-01-15')
            include_details: Whether to include detailed invoice breakdown
            
        Returns:
            Dictionary containing revenue metrics and summary data
            
        Raises:
            ValueError: If parameters are invalid
        """
        # Note: This tool calculates revenue by fetching all invoices in date range
        # and computing metrics from the invoice data
        
        # Fetch all invoices for the date range
        all_invoices = []
        current_item = 0
        page_size = 100  # Use max page size for efficiency
        
        while True:
            # Get invoices for current page using purchase date filter
            invoice_response = await self.api_client.get_invoices(
                from_purchase_date=from_date,
                to_purchase_date=to_date,
                page_size=page_size,
                current_item=current_item,
                order_direction="Asc"
            )
            
            # Convert to dict to access data
            response_data = invoice_response.model_dump()
            invoices = response_data.get("data", [])
            
            if not invoices:
                break
                
            all_invoices.extend(invoices)
            
            # Check if we have more pages
            total = response_data.get("total", 0)
            if current_item + len(invoices) >= total:
                break
                
            current_item += len(invoices)
        
        # Calculate revenue metrics
        total_revenue = 0
        total_invoices = len(all_invoices)
        invoice_details = []
        
        for invoice in all_invoices:
            # Extract revenue from invoice (adjust field names based on actual API response)
            invoice_total = invoice.get("total", 0) or invoice.get("totalAmount", 0)
            total_revenue += invoice_total
            
            if include_details:
                invoice_details.append({
                    "id": invoice.get("id"),
                    "code": invoice.get("code"),
                    "purchaseDate": invoice.get("purchaseDate"),
                    "total": invoice_total,
                    "customer": invoice.get("customer", {}).get("name", "N/A")
                })
        
        # Calculate average
        average_revenue = total_revenue / total_invoices if total_invoices > 0 else 0
        
        # Build result
        result = {
            "dateRange": {
                "fromDate": str(from_date),
                "toDate": str(to_date)
            },
            "summary": {
                "totalRevenue": total_revenue,
                "totalInvoices": total_invoices,
                "averageRevenuePerInvoice": average_revenue
            }
        }
        
        if include_details:
            result["invoiceDetails"] = invoice_details
        
        return result