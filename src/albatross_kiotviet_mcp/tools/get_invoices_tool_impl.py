"""Triển khai công cụ hóa đơn với các tùy chọn lọc toàn diện."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class InvoicesToolImpl(BaseTool):
    """Triển khai công cụ hóa đơn với khả năng lọc toàn diện."""
    
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        branch_ids: Optional[List[int]] = None,
        customer_ids: Optional[List[int]] = None,
        customer_code: Optional[str] = None,
        status: Optional[List[int]] = None,
        include_payment: Optional[bool] = None,
        include_invoice_delivery: Optional[bool] = None,
        last_modified_from: Optional[str] = None,
        to_date: Optional[str] = None,
        created_date: Optional[str] = None,
        from_purchase_date: Optional[str] = None,
        to_purchase_date: Optional[str] = None,
        order_id: Optional[int] = None,
        order_by: Optional[str] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ hóa đơn với các tùy chọn lọc toàn diện.
        
        Args:
            page_size:                  Số lượng mục trên mỗi trang
            current_item:               Chỉ số mục bắt đầu cho phân trang
            order_direction:            Hướng sắp xếp
            from_purchase_date:         Ngày bắt đầu cho bộ lọc ngày mua
            to_purchase_date:           Ngày kết thúc cho bộ lọc ngày mua
            from_created_date:          Ngày bắt đầu cho bộ lọc ngày tạo
            to_created_date:            Ngày kết thúc cho bộ lọc ngày tạo
            from_modified_date:         Ngày bắt đầu cho bộ lọc ngày sửa đổi
            to_modified_date:           Ngày kết thúc cho bộ lọc ngày sửa đổi
            ids:                        Danh sách ID hóa đơn cụ thể cần lấy
            branch_ids:                 Danh sách ID chi nhánh để lọc
            status:                     Bộ lọc trạng thái hóa đơn
            customer_ids:               Danh sách ID khách hàng để lọc
            order_by:                   Trường để sắp xếp theo
            include_invoice_details:    Bao gồm chi tiết dòng hóa đơn
            
        Returns:
            Dictionary chứa dữ liệu hóa đơn từ API
            
        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        
        # Xác thực tham số bằng phương thức lớp cơ sở
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Thực hiện gọi API
        result = await self.api_client.get_invoices(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            branch_ids=branch_ids,
            customer_ids=customer_ids,
            customer_code=customer_code,
            status=status,
            include_payment=include_payment,
            include_invoice_delivery=include_invoice_delivery,
            last_modified_from=last_modified_from,
            to_date=to_date,
            created_date=created_date,
            from_purchase_date=from_purchase_date,
            to_purchase_date=to_purchase_date,
            order_id=order_id,
            order_by=order_by
        )
        
        logger.info(f"Đã lấy thành công {len(result.data)} hóa đơn trong tổng số {result.total}")
        
        # Chuyển đổi thành dictionary để đầu ra nhất quán
        return result.model_dump()