"""Middleware functions for tool execution."""

from functools import wraps
from typing import Callable, Any, Type
from loguru import logger
import time
from pydantic import BaseModel, ValidationError


def with_logging(func: Callable) -> Callable:
    """Middleware để log tool execution.
    
    Args:
        func: Function to wrap with logging
        
    Returns:
        Wrapped function with logging
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        func_name = func.__name__
        logger.info(f"Executing tool: {func_name}")
        logger.debug(f"Tool {func_name} called with args: {args}, kwargs: {kwargs}")
        
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"Tool {func_name} completed successfully in {duration:.2f}s")
            logger.debug(f"Tool {func_name} result keys: {list(result.keys()) if isinstance(result, dict) else 'non-dict result'}")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Tool {func_name} failed after {duration:.2f}s: {e}")
            logger.exception(f"Full error details for {func_name}")
            raise
    return wrapper


def with_validation(schema_class: Type[BaseModel]):
    """Middleware để validate parameters với Pydantic.
    
    Args:
        schema_class: Pydantic model class for validation
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                # Validate parameters using Pydantic schema
                validated_params = schema_class(**kwargs)
                logger.debug(f"Parameters validated successfully for {func.__name__}")
                
                # Call function with validated parameters
                return await func(*args, **validated_params.model_dump())
                
            except ValidationError as e:
                logger.error(f"Parameter validation failed for {func.__name__}: {e}")
                # Re-raise with more context
                raise ValueError(f"Invalid parameters for {func.__name__}: {e}")
            except Exception as e:
                logger.error(f"Unexpected error in validation middleware for {func.__name__}: {e}")
                raise
        return wrapper
    return decorator


def with_error_handling(func: Callable) -> Callable:
    """Middleware để handle common errors.
    
    Args:
        func: Function to wrap with error handling
        
    Returns:
        Wrapped function with error handling
    """
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ValueError as e:
            # Parameter validation errors
            logger.warning(f"Validation error in {func.__name__}: {e}")
            raise
        except Exception as e:
            # Unexpected errors
            logger.error(f"Unexpected error in {func.__name__}: {e}")
            raise RuntimeError(f"Tool execution failed: {e}") from e
    return wrapper