"""Base tool class for KiotViet MCP operations."""

from abc import ABC, abstractmethod
from typing import Dict, Any
from ...api.api_client import KiotVietAPIClient


class BaseTool(ABC):
    """Base class cho tất cả MCP tools với common functionality."""
    
    def __init__(self, api_client: KiotVietAPIClient):
        """Initialize tool with API client.
        
        Args:
            api_client: KiotViet API client instance
        """
        self.api_client = api_client
    
    def validate_pagination(self, page_size: int, current_item: int) -> None:
        """Common pagination validation.
        
        Args:
            page_size: Number of items per page
            current_item: Starting item index
            
        Raises:
            ValueError: If parameters are invalid
        """
        if not (1 <= page_size <= 100):
            raise ValueError("page_size must be between 1 and 100")
        if current_item < 0:
            raise ValueError("current_item must be non-negative")
    
    def validate_order_direction(self, order_direction: str) -> None:
        """Common order direction validation.
        
        Args:
            order_direction: Sort order direction
            
        Raises:
            ValueError: If order direction is invalid
        """
        if order_direction not in ['Asc', 'Desc']:
            raise ValueError("order_direction must be 'Asc' or 'Desc'")
    
    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Abstract method mỗi tool phải implement.
        
        Args:
            **kwargs: Tool-specific parameters
            
        Returns:
            Dictionary containing tool execution result
        """
        pass