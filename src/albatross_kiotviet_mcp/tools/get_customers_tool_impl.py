"""Triển khai công cụ khách hàng với các tùy chọn lọc toàn diện."""

from typing import Dict, Any, Optional, List
from loguru import logger
from .core.base_tool import BaseTool


class CustomersToolImpl(BaseTool):
    """Triển khai công cụ khách hàng với khả năng lọc toàn diện."""
    
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        code: Optional[str] = None,
        name: Optional[str] = None,
        contact_number: Optional[str] = None,
        last_modified_from: Optional[str] = None,
        order_by: Optional[str] = None,
        include_remove_ids: Optional[bool] = None,
        include_total: Optional[bool] = None,
        include_customer_group: Optional[bool] = None,
        include_customer_social: Optional[bool] = None,
        birth_date: Optional[str] = None,
        group_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Thực thi công cụ khách hàng với các tùy chọn lọc theo API chính thức.
        
        Args:
            page_size: S<PERSON> lượng mục trên mỗi trang
            current_item: Chỉ số mục bắt đầu cho phân trang
            order_direction: Hướng sắp xếp
            code: Tìm kiếm khách hàng theo mã khách hàng
            name: Tìm kiếm theo tên khách hàng
            contact_number: Tìm kiếm theo số điện thoại khách hàng
            last_modified_from: Thời gian cập nhật
            order_by: Sắp xếp dữ liệu theo trường
            include_remove_ids: Có lấy thông tin danh sách ID bị xóa
            include_total: Có lấy thông tin TotalInvoice, TotalPoint, TotalRevenue
            include_customer_group: Có lấy thông tin nhóm khách hàng
            include_customer_social: Có lấy thông tin Psid facebook fanpage
            birth_date: Lọc khách hàng theo ngày sinh nhật
            group_id: Lọc theo nhóm khách hàng
            
        Returns:
            Dictionary chứa dữ liệu khách hàng từ API
            
        Raises:
            ValueError: Nếu tham số không hợp lệ
        """
        
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Thực hiện gọi API
        result = await self.api_client.get_customers(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            code=code,
            name=name,
            contact_number=contact_number,
            last_modified_from=last_modified_from,
            order_by=order_by,
            include_remove_ids=include_remove_ids,
            include_total=include_total,
            include_customer_group=include_customer_group,
            include_customer_social=include_customer_social,
            birth_date=birth_date,
            group_id=group_id
        )
        
        logger.info(f"Đã lấy thành công {len(result.data)} khách hàng trong tổng số {result.total}")
        
        # Chuyển đổi thành dictionary để đầu ra nhất quán
        return result.model_dump()