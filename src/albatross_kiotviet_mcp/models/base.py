"""Base models and common types for KiotViet API responses."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """Base response model for paginated API endpoints."""
    total: int = Field(description="Total number of items")
    pageSize: int = Field(description="Items per page")


class ProductBatchExpire(BaseModel):
    """Mô hình dữ liệu lô hàng có hạn sử dụng."""
    id: int
    productId: int
    batchName: str
    fullNameVirgule: str
    createdDate: Optional[datetime] = None
    expireDate: Optional[datetime] = None


class SaleChannel(BaseModel):
    """Mô hình dữ liệu kênh bán hàng."""
    IsNotDelete: bool
    RetailerId: int
    Position: int
    IsActivate: bool
    CreatedBy: int
    CreatedDate: Optional[datetime] = None
    Id: int
    Name: str


class PartnerDelivery(BaseModel):
    """<PERSON><PERSON> hình dữ liệu đối tác giao hàng."""
    code: str
    name: str
    address: str
    contactNumber: str
    email: str