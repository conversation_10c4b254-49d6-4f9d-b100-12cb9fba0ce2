"""Purchase order models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse, ProductBatchExpire


class PurchaseOrderDetail(BaseModel):
    """Mô hình dữ liệu chi tiết đơn nhập hàng."""
    productId: int = Field(description="ID sản phẩm")
    ProductCode: Optional[str] = None  # Trường này có thể không có trong response
    productName: str = Field(description="Tên sản phẩm")
    quantity: float = Field(description="Số lượng")
    price: float = Field(description="Giá")
    discount: Optional[float] = None  # API trả về float, không phải string
    serialNumbers: Optional[str] = None
    productBatchExpire: Optional[ProductBatchExpire] = None


class PurchaseOrder(BaseModel):
    """<PERSON><PERSON> hình dữ liệu đơn nhập hàng riêng lẻ."""
    id: int = Field(description="ID đơn nhập hàng")
    code: str = Field(description="Mã đơn nhập hàng")
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    purchaseDate: Optional[datetime] = None
    discountRatio: Optional[float] = None
    total: Optional[float] = None
    supplierId: Optional[int] = None
    supplierName: Optional[str] = None
    supplierCode: Optional[str] = None
    partnerType: Optional[str] = None
    purchaseById: Optional[int] = None
    purchaseName: Optional[str] = None  # API trả về string (tên người mua), không phải int
    purchaseOrderDetails: Optional[List[PurchaseOrderDetail]] = None


class PurchaseOrderResponse(BaseResponse):
    """Mô hình phản hồi cho endpoint API đơn nhập hàng."""
    data: List[PurchaseOrder] = Field(description="Dữ liệu đơn nhập hàng")