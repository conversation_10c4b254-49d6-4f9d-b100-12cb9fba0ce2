"""Category models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse


class Category(BaseModel):
    """Individual category data model."""
    id: int = Field(alias="categoryId")  # KiotViet API uses 'categoryId' instead of 'id'
    categoryName: str
    categoryCode: Optional[str] = None
    parentId: Optional[int] = None
    hasChild: bool = False
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    rank: Optional[int] = None  # Add rank field that appears in API response


class CategoryResponse(BaseResponse):
    """Response model for categories API endpoint."""
    data: List[Category]