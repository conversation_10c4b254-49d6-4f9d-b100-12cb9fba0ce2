"""Branch models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse


class Branch(BaseModel):
    """Individual branch data model."""
    id: int = Field(description="Branch ID")
    branchName: str = Field(description="Branch name")
    contactNumber: Optional[str] = None
    address: Optional[str] = None
    locationName: Optional[str] = None
    isActive: Optional[bool] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None


class BranchResponse(BaseResponse):
    """Response model for branches API endpoint."""
    data: List[Branch] = Field(description="Branch data")