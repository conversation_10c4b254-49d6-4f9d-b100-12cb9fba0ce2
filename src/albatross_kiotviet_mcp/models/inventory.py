"""Inventory models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse


class Inventory(BaseModel):
    """Individual inventory data for a branch."""
    branchId: int = Field(description="Branch ID")
    onHand: float = Field(description="Available stock quantity")
    reserved: float = Field(description="Reserved stock quantity")


class ProductOnHand(BaseModel):
    """Individual product with inventory data."""
    id: int = Field(description="Product ID")
    code: str = Field(description="Product code")
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    inventories: List[Inventory] = Field(description="Inventory data for each branch")


class ProductOnHandResponse(BaseResponse):
    """Response model for product on hand API endpoint."""
    data: List[ProductOnHand] = Field(description="Product inventory data")