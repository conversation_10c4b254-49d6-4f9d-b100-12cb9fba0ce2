"""Order models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse


class OrderDetail(BaseModel):
    """Mô hình dữ liệu chi tiết đơn hàng."""
    productId: int = Field(description="ID sản phẩm")
    productCode: str = Field(description="Mã sản phẩm")
    productName: str = Field(description="Tên sản phẩm")
    quantity: float = Field(description="Số lượng")
    price: float = Field(description="Giá")
    discount: Optional[float] = None
    discountRatio: Optional[float] = None
    note: Optional[str] = None
    viewDiscount: Optional[float] = None


class OrderPayment(BaseModel):
    """Mô hình dữ liệu thanh toán đơn hàng."""
    id: int = Field(description="ID thanh toán")
    code: str = Field(description="Mã thanh toán")
    amount: float = Field(description="Số tiền")
    accountId: Optional[int] = None
    bankAccount: Optional[str] = None
    description: Optional[str] = None
    method: str = Field(description="Phương thức thanh toán")
    status: int = Field(description="Trạng thái thanh toán")
    statusValue: str = Field(description="Giá trị trạng thái")
    transDate: Optional[datetime] = None


class Order(BaseModel):
    """Mô hình dữ liệu đơn hàng riêng lẻ."""
    id: int = Field(description="ID đơn hàng")
    code: str = Field(description="Mã đơn hàng")
    purchaseDate: Optional[datetime] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    soldById: Optional[int] = None
    soldByName: Optional[str] = None
    customerId: Optional[int] = None
    customerCode: Optional[str] = None
    customerName: Optional[str] = None
    total: Optional[float] = None
    totalPayment: Optional[float] = None
    status: int = Field(description="Trạng thái đơn hàng")
    statusValue: str = Field(description="Giá trị trạng thái")
    retailerId: Optional[int] = None
    description: Optional[str] = None
    usingCod: Optional[bool] = None
    modifiedDate: Optional[datetime] = None
    createdDate: Optional[datetime] = None
    SaleChannelId: Optional[int] = None
    PriceBookId: Optional[int] = None
    Extra: Optional[str] = None
    SaleChannelName: Optional[str] = None
    orderDetails: Optional[List[OrderDetail]] = None
    payments: Optional[List[OrderPayment]] = None


class OrderResponse(BaseResponse):
    """Mô hình phản hồi cho endpoint API đơn hàng."""
    data: List[Order] = Field(description="Dữ liệu đơn hàng")