"""Data models package for KiotViet API responses."""

from .base import BaseResponse, ProductBatchExpire, SaleChannel, PartnerDelivery
from .categories import Category, CategoryResponse
from .branches import Branch, BranchResponse
from .inventory import Inventory, ProductOnHand, ProductOnHandResponse
from .products import Product
from .invoices import (
    Invoice,
    InvoiceResponse,
    InvoicePayment,
    InvoiceOrderSurcharge,
    InvoiceDetail,
    InvoiceDelivery
)

from .customers import Customer, CustomerResponse
from .purchase_orders import PurchaseOrder, PurchaseOrderResponse, PurchaseOrderDetail
from .orders import Order, OrderResponse, OrderDetail, OrderPayment
from .returns import Return, ReturnResponse, ReturnDetail
from .cashflow import CashFlow, CashFlowResponse

__all__ = [
    # Base
    "BaseResponse",
    "ProductBatchExpire",
    "SaleChannel",
    "PartnerDelivery",
    
    # Categories
    "Category",
    "CategoryResponse",
    
    # Branches
    "Branch",
    "BranchResponse", 
    
    # Inventory
    "Inventory",
    "ProductOnHand",
    "ProductOnHandResponse",
    
    # Products
    "Product",
    
    # Invoices
    "Invoice", 
    "InvoiceResponse",
    "InvoicePayment",
    "InvoiceOrderSurcharge",
    "InvoiceDetail", 
    "InvoiceDelivery",
    
    # Customers
    "Customer",
    "CustomerResponse",
    
    # Purchase Orders
    "PurchaseOrder",
    "PurchaseOrderResponse",
    "PurchaseOrderDetail",
    
    # Orders
    "Order",
    "OrderResponse",
    "OrderDetail",
    "OrderPayment",
    
    # Returns
    "Return",
    "ReturnResponse",
    "ReturnDetail",
    
    # Cashflow
    "CashFlow",
    "CashFlowResponse"
]
