"""Product models for KiotViet API."""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class Product(BaseModel):
    """Individual product data model."""
    id: int = Field(description="Product ID")
    code: str = Field(description="Product code")
    name: str = Field(description="Product name")
    fullName: Optional[str] = None
    categoryId: Optional[int] = None
    categoryName: Optional[str] = None
    basePrice: Optional[float] = None
    isActive: Optional[bool] = None
    allowsSale: Optional[bool] = None
    isService: Optional[bool] = None
    hasVariants: Optional[bool] = None
    description: Optional[str] = None
    weight: Optional[float] = None
    conversionValue: Optional[float] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None