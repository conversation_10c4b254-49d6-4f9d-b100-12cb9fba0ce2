"""Return models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse


class ReturnDetail(BaseModel):
    """Mô hình dữ liệu chi tiết đơn trả hàng."""
    productId: int = Field(description="ID sản phẩm")
    productCode: str = Field(description="Mã sản phẩm")
    productName: str = Field(description="Tên sản phẩm")
    quantity: float = Field(description="Số lượng")
    price: float = Field(description="Giá")
    subTotal: float = Field(description="Thành tiền")


class Return(BaseModel):
    """Mô hình dữ liệu đơn trả hàng riêng lẻ theo API response."""
    id: int = Field(description="ID đơn trả hàng")
    code: str = Field(description="Mã đơn trả hàng")
    invoiceId: Optional[int] = None
    returnDate: Optional[datetime] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    receivedById: Optional[int] = None
    soldByName: Optional[str] = None
    customerId: Optional[int] = None
    customerCode: Optional[str] = None
    customerName: Optional[str] = None
    returnTotal: Optional[float] = None
    returnDiscount: Optional[float] = None
    totalPayment: Optional[float] = None
    returnFee: Optional[float] = None
    status: Optional[int] = None
    statusValue: Optional[str] = None
    modifiedDate: Optional[datetime] = None
    createdDate: Optional[datetime] = None
    returnDetails: Optional[List[ReturnDetail]] = None


class ReturnResponse(BaseResponse):
    """Mô hình phản hồi cho endpoint API đơn trả hàng."""
    data: List[Return] = Field(description="Dữ liệu đơn trả hàng")