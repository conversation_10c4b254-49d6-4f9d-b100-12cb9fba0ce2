"""Cashflow models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse


class CashFlow(BaseModel):
    """Mô hình dữ liệu phiếu thu chi riêng lẻ theo API response."""
    id: int = Field(description="ID phiếu thu chi")
    code: str = Field(description="Mã phiếu thu chi")
    userId: Optional[int] = None
    address: Optional[str] = None
    contactNumber: Optional[str] = None
    status: Optional[int] = None
    createdBy: Optional[int] = None
    usedForFinancialReporting: Optional[int] = None
    origin: Optional[str] = None
    cashFlowGroupId: Optional[int] = None
    cashGroup: Optional[str] = None
    statusValue: Optional[str] = None
    method: Optional[str] = None
    description: Optional[str] = None
    partnerType: Optional[str] = None
    partnerId: Optional[int] = None
    branchId: Optional[int] = None
    retailerId: Optional[int] = None
    transDate: Optional[datetime] = None
    amount: Optional[float] = None
    partnerName: Optional[str] = None


class CashFlowResponse(BaseResponse):
    """Mô hình phản hồi cho endpoint API phiếu thu chi."""
    data: List[CashFlow] = Field(description="Dữ liệu phiếu thu chi")