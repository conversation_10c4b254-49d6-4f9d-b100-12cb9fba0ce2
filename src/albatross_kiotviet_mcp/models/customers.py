"""Customer models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse


class Customer(BaseModel):
    """Mô hình dữ liệu khách hàng riêng lẻ."""
    id: int = Field(description="ID khách hàng")
    code: str = Field(description="Mã khách hàng")
    name: str = Field(description="Tên khách hàng")
    contactNumber: Optional[str] = None
    address: Optional[str] = None
    wardName: Optional[str] = None
    locationName: Optional[str] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    debt: Optional[float] = None
    totalInvoiced: Optional[float] = None
    totalPoint: Optional[float] = None
    email: Optional[str] = None
    birthDate: Optional[datetime] = None
    gender: Optional[bool] = None
    isActive: Optional[bool] = None
    createdDate: Optional[datetime] = None
    modifiedDate: Optional[datetime] = None
    retailerId: Optional[int] = None
    groupId: Optional[int] = None
    groupName: Optional[str] = None


class CustomerResponse(BaseResponse):
    """Mô hình phản hồi cho endpoint API khách hàng."""
    data: List[Customer] = Field(description="Dữ liệu khách hàng")