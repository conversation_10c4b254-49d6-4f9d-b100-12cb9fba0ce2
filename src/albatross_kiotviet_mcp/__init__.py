"""Albatross KiotViet MCP Server.

A Model Context Protocol (MCP) server for integrating with KiotViet API.
Provides tools for managing categories, invoices, and revenue calculations.
"""

from .server import main as server_main
from .config import get_settings

__version__ = "0.1.0"
__author__ = "Albatross Foundation"

# Public API
__all__ = [
    "server_main",
    "get_settings",
    "__version__",
    "__author__"
]
