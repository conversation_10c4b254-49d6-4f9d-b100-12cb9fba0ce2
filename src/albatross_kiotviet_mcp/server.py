"""KiotViet MCP Server - Clean separation with individual tool files."""

from loguru import logger
import sys

from .config import get_settings
from .tools import register_all_tools

# Get settings and create server
settings = get_settings()

# Import FastMCP here to avoid circular imports
from fastmcp import FastMCP
mcp = FastMCP(settings.server_name)

# Register all tools from separate files
register_all_tools(mcp)


# Add health check endpoint for production monitoring
@mcp.custom_route("/health", methods=["GET"])
async def health_check(request):
    """Health check endpoint for monitoring and load balancers."""
    from starlette.responses import JSONResponse

    try:
        # Basic health check - verify settings can be loaded
        health_settings = get_settings()

        return JSONResponse({
            "status": "healthy",
            "server": health_settings.server_name,
            "transport": health_settings.mcp_transport,
            "version": "0.1.0"
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            {"status": "unhealthy", "error": str(e)},
            status_code=503
        )


def main() -> None:
    """Entry point for running the FastMCP server."""
    logger.info(f"Starting {settings.server_name}...")

    # Validate configuration on startup
    try:
        logger.info(f"Configuration loaded successfully for retailer: {settings.kiotviet_retailer}")
        logger.info(f"API Base URL: {settings.kiotviet_api_base_url}")
        logger.info(f"Request timeout: {settings.kiotviet_request_timeout}s")
        logger.info(f"Transport mode: {settings.mcp_transport}")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        sys.exit(1)

    logger.info("All tools registered successfully from separate files")

    # Check transport mode from settings
    if settings.mcp_transport.lower() == "http":
        logger.info("🌐 Starting server with Streamable HTTP transport")
        logger.info(f"📍 Server URL: http://{settings.mcp_host}:{settings.mcp_port}{settings.mcp_path}")
        logger.info("🔗 This server supports the Streamable HTTP protocol for MCP")

        try:
            return mcp.run(
                transport="http",
                host=settings.mcp_host,
                port=settings.mcp_port,
                path=settings.mcp_path,
                log_level=settings.log_level.lower()
            )
        except Exception as e:
            logger.error(f"Failed to start HTTP server: {e}")
            sys.exit(1)
    else:
        logger.info("💻 Starting server with STDIO transport")
        try:
            return mcp.run()
        except Exception as e:
            logger.error(f"Failed to start STDIO server: {e}")
            sys.exit(1)


# Backward compatibility
def run() -> None:
    """Backward compatibility function."""
    return main()