"""Simplified configuration management for KiotViet MCP Server."""

from typing import Optional
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Simplified configuration for KiotViet API integration."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # KiotViet API credentials
    kiotviet_client_id: str = Field(..., description="KiotViet Client ID")
    kiotviet_client_secret: str = Field(..., description="KiotViet Client Secret")
    kiotviet_retailer: str = Field(..., description="KiotViet Retailer Name")
    
    # API endpoints
    kiotviet_auth_url: str = Field(
        default="https://id.kiotviet.vn/connect/token",
        description="KiotViet OAuth2 token endpoint"
    )
    kiotviet_api_base_url: str = Field(
        default="https://public.kiotapi.com",
        description="KiotViet API base URL"
    )
    
    # Token settings
    kiotviet_token_buffer_seconds: int = Field(
        default=300,  # Refresh token 5 minutes before expiry
        description="Token refresh buffer in seconds"
    )
    
    # Request settings
    kiotviet_request_timeout: int = Field(
        default=30,
        description="HTTP request timeout in seconds"
    )
    kiotviet_max_retries: int = Field(
        default=3,
        description="Maximum number of retry attempts"
    )
    kiotviet_retry_delay: float = Field(
        default=1.0,
        description="Base delay between retry attempts in seconds"
    )
    
    # MCP Transport settings
    mcp_transport: str = Field(
        default="stdio",
        description="MCP transport mode: 'stdio' or 'http'"
    )
    mcp_host: str = Field(
        default="127.0.0.1",
        description="HTTP server host binding"
    )
    mcp_port: int = Field(
        default=8000,
        description="HTTP server port"
    )
    mcp_path: str = Field(
        default="/mcp/",
        description="MCP endpoint path"
    )
    
    # Logging settings
    log_level: str = Field(default="info", description="Logging level (INFO, DEBUG, WARNING, ERROR)")
    log_file: Optional[str] = Field(default=None, description="Log file path")
    
    # Server settings
    server_name: str = Field(default="KiotVietMCPServer", description="MCP server name")
