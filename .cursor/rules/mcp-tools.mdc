# MCP Tool Implementation Rules

## Function-Based Tool Pattern (RECOMMENDED)
Every MCP tool should be implemented as a simple function with FastMCP decorators:

1. Use `@mcp.tool()` decorator for registration
2. Return `Dict[str, Any]` for consistent output
3. Use utility functions for validation and result processing
4. Let FastMCP handle errors naturally

```python
# ✅ RECOMMENDED PATTERN
def register_tool_name(mcp: FastMCP) -> None:
    """Register the tool with the FastMCP server."""

    @mcp.tool()
    async def tool_function(
        param1: Annotated[str, Field(description="Parameter description")],
        param2: Annotated[int, Field(description="Another parameter", ge=1, le=100)] = 50
    ) -> Dict[str, Any]:
        """Tool function docstring with clear description."""
        # Validate parameters using utility functions
        validate_parameters(param1, param2)

        # Make API call
        api_client = get_api_client()
        async with api_client:
            result = await api_client.method_name(param1=param1, param2=param2)

        # Process result to ensure consistent dictionary output
        return process_mcp_result(result)
```

## API Client Pattern (REQUIRED)
- All API methods must return typed Pydantic responses
- Use make_request() from base client
- Validate responses with model_validate()

```python
# ✅ CORRECT API CLIENT METHOD
async def get_categories(
    self,
    page_size: int = 50,
    current_item: int = 0
) -> CategoryResponse:
    data = {"pageSize": page_size, "currentItem": current_item}
    raw_response = await self.make_request("GET", "/categories", data=data)
    return CategoryResponse.model_validate(raw_response)
```

## Utility Functions (REQUIRED)

### Parameter Validation
Use utility functions from `tools/utils.py`:
```python
from .utils import (
    validate_pagination_params,
    validate_order_direction,
    parse_and_validate_dates,
    process_mcp_result
)
```

### Result Processing
Always use `process_mcp_result()` to handle Pydantic models:
```python
# ✅ CORRECT - Handles Pydantic models automatically
return process_mcp_result(api_result)

# ❌ WRONG - Manual handling
if hasattr(api_result, 'model_dump'):
    return api_result.model_dump()
```

## Forbidden Patterns

### ❌ NEVER DO THESE:
- Using class-based BaseMCPTool pattern (deprecated)
- Manual error handling (let FastMCP handle it)
- Missing `process_mcp_result()` call
- Direct dictionary returns without processing
- Complex decorator patterns
description:
globs:
alwaysApply: false
---
