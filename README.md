# 🚀 KiotViet MCP Server

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![FastMCP](https://img.shields.io/badge/FastMCP-2.2.0+-green.svg)](https://github.com/jlowin/fastmcp)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A professional **Model Context Protocol (MCP)** server for seamless integration with **KiotViet Public API**. This server enables AI assistants like <PERSON> to interact with KiotViet's retail management system through natural language queries, providing powerful retail analytics and data access capabilities.

## ✨ Features

- **🏗️ Clean Architecture**: Modular design with clear separation of concerns
- **🔐 Secure Authentication**: Automatic OAuth2 token management with refresh handling
- **🛠️ 10 Comprehensive MCP Tools**: Complete retail data access (categories, products, invoices, inventory, etc.)
- **🔒 Type Safety**: Complete type hints with Pydantic validation throughout
- **🧪 Production Ready**: Comprehensive error handling, logging, and async/await
- **⚡ High Performance**: Optimized API calls with retry logic and connection pooling
- **🔧 Extensible**: Easy-to-follow patterns for adding new tools
- **📊 Rich Analytics**: Revenue calculations, inventory tracking, customer insights

## 📁 Project Structure

This project follows **Clean Architecture** principles with domain-driven design:

```
src/albatross_kiotviet_mcp/
├── api/                          # 🌐 API Integration Layer
│   ├── api_client.py            # Main KiotViet API client
│   └── auth.py                  # OAuth2 authentication manager
├── config/                       # ⚙️ Configuration Management
│   ├── config.py                # Settings with environment variables
│   └── dependencies.py          # Dependency injection
├── models/                       # 📋 Data Models (Recently Refactored)
│   ├── base.py                  # Base response models
│   ├── categories.py            # Category models
│   ├── branches.py              # Branch models
│   ├── inventory.py             # Inventory/stock models
│   ├── products.py              # Product models
│   ├── invoices.py              # Invoice models
│   ├── customers.py             # Customer models
│   ├── purchase_orders.py       # Purchase order models
│   ├── returns.py               # Return models
│   └── cashflow.py              # Cashflow models
├── tools/                        # 🔧 MCP Tools Implementation
│   ├── core/                    # Base classes and middleware
│   │   ├── base_tool.py         # Abstract base tool class
│   │   └── middleware.py        # Logging and validation decorators
│   ├── schemas/                 # Parameter validation schemas
│   │   ├── common_schemas.py    # Shared parameter types
│   │   ├── branches_schema.py   # Branch tool parameters
│   │   ├── categories_schema.py # Category tool parameters
│   │   ├── inventory_schema.py  # Inventory tool parameters
│   │   ├── invoices_schema.py   # Invoice tool parameters
│   │   ├── customers_schema.py  # Customer tool parameters
│   │   ├── purchase_orders_schema.py # Purchase order parameters
│   │   ├── returns_schema.py    # Return tool parameters
│   │   ├── cashflow_schema.py   # Cashflow tool parameters
│   │   └── revenue_schema.py    # Revenue calculation parameters
│   ├── register_tools.py        # Central tool registration
│   ├── get_categories_tool_impl.py      # Categories retrieval
│   ├── get_branches_tool_impl.py        # Branches retrieval
│   ├── get_inventory_tool_impl.py       # Inventory/stock data
│   ├── get_invoices_tool_impl.py        # Invoice retrieval
│   ├── get_product_by_code_tool_impl.py # Product lookup
│   ├── get_customers_tool_impl.py       # Customer management
│   ├── get_purchase_orders_tool_impl.py # Purchase orders
│   ├── get_returns_tool_impl.py         # Returns management
│   ├── get_cashflow_tool_impl.py        # Cashflow tracking
│   └── calculate_daily_revenue_tool_impl.py # Revenue analytics
├── main.py                       # 🚀 Application entry point
└── server.py                     # 🖥️ FastMCP server setup
```

### 🎯 Architecture Benefits

- **🔄 Modular Design**: Each tool is self-contained with clear responsibilities
- **🛡️ Type Safety**: Pydantic models ensure data integrity at every layer
- **🔌 Easy Extension**: Add new tools by following established patterns
- **🧪 Testable**: Clean separation enables comprehensive testing
- **📈 Scalable**: Architecture supports growing feature requirements

## 🚀 Getting Started

### 📋 Prerequisites

- **Python 3.10+** (supports 3.10, 3.11, 3.12, 3.13)
- **KiotViet API Credentials**: Client ID, Client Secret, Retailer name
- **uv** (recommended) or pip for dependency management
- **Claude Desktop** or compatible MCP client

### 1️⃣ Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd albatross-kiotviet-mcp

# Install dependencies with uv (recommended)
uv sync

# Or with pip
pip install -e .
```

### 2️⃣ Configuration

Create a `.env` file with your KiotViet credentials:

```env
# Required KiotViet API credentials
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_name_here

# Optional configuration (with defaults)
KIOTVIET_AUTH_URL=https://id.kiotviet.vn/connect/token
KIOTVIET_API_BASE_URL=https://public.kiotapi.com
KIOTVIET_REQUEST_TIMEOUT=30
KIOTVIET_MAX_RETRIES=3
KIOTVIET_RETRY_DELAY=1
LOG_LEVEL=INFO
```

### 3️⃣ Run the MCP Server

```bash
# Using uv (recommended)
uv run albatross-kiotviet-mcp-server

# Or using the installed script
albatross-kiotviet-mcp-server

# Development mode with auto-reload
uv run python -m albatross_kiotviet_mcp.main

# Using make (if available)
make run
```

### 4️⃣ Connect from Claude Desktop

Edit your Claude Desktop configuration file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`  
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "kiotviet": {
      "command": "uv",
      "args": [
        "--directory",
        "/absolute/path/to/albatross-kiotviet-mcp",
        "run",
        "albatross-kiotviet-mcp-server"
      ],
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

**⚠️ Important**: Use absolute paths in the configuration.

Restart Claude Desktop to load the configuration.

### 5️⃣ Connect from Cursor IDE

For Cursor IDE integration, create or update your MCP configuration:

**Cursor Settings** → **Extensions** → **MCP** → **Add Server**

```json
{
  "name": "kiotviet",
  "command": "uv",
  "args": [
    "--directory",
    "/absolute/path/to/albatross-kiotviet-mcp",
    "run",
    "albatross-kiotviet-mcp-server"
  ],
  "env": {
    "PYTHONUNBUFFERED": "1"
  }
}
```

## 🛠️ Available MCP Tools

This server provides **10 comprehensive tools** for KiotViet data access:

### 📊 **Core Business Data**

| Tool | Description | Key Features |
|------|-------------|--------------|
| **`get_categories`** | Retrieve product categories | • Hierarchical structure<br>• Pagination support<br>• Parent-child relationships |
| **`get_branches`** | Get store branches | • Branch details<br>• Location information<br>• Active status filtering |
| **`get_products`** | Product information lookup | • Search by product code<br>• Detailed product data<br>• Category associations |
| **`get_inventory`** | Stock and inventory data | • Real-time stock levels<br>• Multi-branch inventory<br>• Reserved quantities |

### 💰 **Financial & Transaction Data**

| Tool | Description | Key Features |
|------|-------------|--------------|
| **`get_invoices`** | Invoice management | • Date range filtering<br>• Branch-specific data<br>• Status filtering<br>• Payment information |
| **`calculate_daily_revenue`** | Revenue analytics | • Daily revenue calculation<br>• Date range analysis<br>• Branch-wise breakdown |
| **`get_cashflow`** | Cash flow tracking | • Income/expense tracking<br>• Transaction categorization<br>• Multi-branch support |

### 👥 **Customer & Supplier Management**

| Tool | Description | Key Features |
|------|-------------|--------------|
| **`get_customers`** | Customer database | • Customer profiles<br>• Contact information<br>• Purchase history<br>• Group filtering |
| **`get_purchase_orders`** | Purchase order tracking | • Supplier management<br>• Order status tracking<br>• Date filtering<br>• Branch filtering |
| **`get_returns`** | Return management | • Return processing<br>• Customer returns<br>• Invoice associations<br>• Status tracking |

### 💡 **Example Usage**

Once connected to Claude or Cursor, you can use natural language queries like:

```
"Show me the revenue for last week"
"What are the top product categories?"
"List all customers from Ho Chi Minh branch"
"Show inventory levels for product ABC123"
"Get all pending purchase orders"
```

## 🔧 Development

### 📦 **Dependencies**

**Core Dependencies:**
- `fastmcp>=2.2.0` - MCP server framework
- `httpx>=0.25.0` - Async HTTP client
- `pydantic>=2.0.0` - Data validation and settings
- `python-dotenv>=0.21.0` - Environment variable management
- `loguru>=0.7.0` - Advanced logging

**Development Dependencies:**
- `pytest>=7.0.0` - Testing framework
- `black>=23.0.0` - Code formatting
- `isort>=5.12.0` - Import sorting
- `mypy>=1.0.0` - Static type checking
- `flake8>=6.0.0` - Linting

### 🧪 **Testing**

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=src

# Run specific test
uv run python tests/debug_categories_simple.py

# Run integration tests (requires API credentials)
uv run pytest -m integration
```

### 🔨 **Development Commands**

```bash
# Setup development environment
make setup

# Run the server
make run

# Format code
make format

# Run linting
make lint

# Run type checking
make typecheck

# Build package
make build

# Clean build artifacts
make clean
```

### 🏗️ **Adding New Tools**

To add a new MCP tool, follow these steps:

1. **Create the model** in `src/albatross_kiotviet_mcp/models/`:
   ```python
   # models/new_feature.py
   from .base import BaseResponse
   
   class NewFeature(BaseModel):
       # Define your model
   
   class NewFeatureResponse(BaseResponse):
       data: List[NewFeature]
   ```

2. **Add API method** in `src/albatross_kiotviet_mcp/api/api_client.py`:
   ```python
   async def get_new_feature(self, **params) -> NewFeatureResponse:
       raw_response = await self.make_request("GET", "/new-endpoint", data=params)
       return NewFeatureResponse.model_validate(raw_response)
   ```

3. **Create parameter schema** in `src/albatross_kiotviet_mcp/tools/schemas/`:
   ```python
   # schemas/new_feature_schema.py
   class NewFeatureParams(BaseModel):
       # Define parameters
   ```

4. **Implement the tool** in `src/albatross_kiotviet_mcp/tools/`:
   ```python
   # get_new_feature_tool_impl.py
   from .core.base_tool import BaseTool
   
   class NewFeatureToolImpl(BaseTool):
       async def execute(self, **params):
           # Implement tool logic
   ```

5. **Register the tool** in `src/albatross_kiotviet_mcp/tools/register_tools.py`:
   ```python
   @mcp.tool()
   async def get_new_feature(**params):
       tool = NewFeatureToolImpl()
       return await tool.execute(**params)
   ```

## 🐳 Docker Support

### **Build and Run with Docker**

```bash
# Build the image
docker build -t albatross-kiotviet-mcp .

# Run with docker-compose
docker-compose up

# Run standalone container
docker run --env-file .env albatross-kiotviet-mcp
```

### **Docker Compose Configuration**

```yaml
# docker-compose.yml
version: '3.8'
services:
  kiotviet-mcp:
    build: .
    env_file: .env
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
```

## 📊 Monitoring & Logging

### **Log Files**

Logs are automatically created in the `logs/` directory:
- **Format**: `kiotviet_mcp_YYYYMMDD.log`
- **Rotation**: Daily rotation with 7-day retention
- **Levels**: DEBUG, INFO, WARNING, ERROR

### **Log Configuration**

```env
# Set log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
```

### **Monitoring API Health**

The server provides built-in health monitoring through structured logging:

```bash
# Monitor logs in real-time
tail -f logs/kiotviet_mcp_$(date +%Y%m%d).log

# Filter for errors
grep "ERROR" logs/kiotviet_mcp_*.log

# Monitor API calls
grep "Making.*request" logs/kiotviet_mcp_*.log
```

## 🔒 Security & Best Practices

### **Environment Variables**

- ✅ **Never commit** `.env` files to version control
- ✅ **Use strong credentials** for KiotViet API
- ✅ **Rotate credentials** regularly
- ✅ **Use environment-specific** configurations

### **API Rate Limiting**

The client includes built-in rate limiting and retry logic:
- **Automatic retries** with exponential backoff
- **Connection pooling** for optimal performance
- **Request timeout** configuration
- **Error handling** for API limits

### **Data Privacy**

- **No sensitive data** is logged in production
- **API responses** are validated and sanitized
- **Customer data** is handled according to privacy standards

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** your changes: `git commit -m 'Add amazing feature'`
4. **Push** to the branch: `git push origin feature/amazing-feature`
5. **Open** a Pull Request

### **Code Standards**

- Follow **PEP 8** style guidelines
- Use **type hints** throughout
- Write **comprehensive tests**
- Update **documentation** for new features
- Follow **conventional commits**

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Troubleshooting

### **Common Issues**

**Authentication Errors:**
```bash
# Check credentials in .env file
cat .env | grep KIOTVIET

# Test API connection
uv run python tests/debug_categories_simple.py
```

**Connection Issues:**
```bash
# Check network connectivity
curl -I https://public.kiotapi.com

# Verify server is running
ps aux | grep albatross-kiotviet-mcp
```

**MCP Connection Issues:**
- Ensure **absolute paths** in MCP configuration
- **Restart** Claude Desktop after configuration changes
- Check **logs** for connection errors

### **Getting Help**

- 📖 **Documentation**: Check the `/docs` folder for detailed guides
- 🐛 **Issues**: Report bugs on GitHub Issues
- 💬 **Discussions**: Join community discussions
- 📧 **Contact**: Reach out to the development team

---

**Made with ❤️ by the Albatross Foundation Team**

*Empowering retail businesses with AI-driven insights through KiotViet integration.*