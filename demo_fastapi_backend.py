#!/usr/bin/env python3
"""
Demo: FastAPI Backend for RAG + MCP Chatbot
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import asyncio
import os
import json
from dotenv import load_dotenv
from demo_rag_mcp_chatbot import RAGMCPChatbot

# Load environment
load_dotenv('.env_demo')

# FastAPI app
app = FastAPI(
    title="KiotViet RAG + MCP Chatbot API",
    description="API for RAG chatbot integrated with KiotViet MCP server",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global chatbot instance
chatbot: Optional[RAGMCPChatbot] = None

# Pydantic models
class ChatRequest(BaseModel):
    query: str
    use_streaming: bool = False
    max_steps: int = 15

class ChatResponse(BaseModel):
    response: str
    query: str
    timestamp: float
    metadata: Dict[str, Any] = {}

class HealthResponse(BaseModel):
    status: str
    chatbot_ready: bool
    mcp_server_url: str
    vector_db_path: str

class SystemInfoResponse(BaseModel):
    chatbot_initialized: bool
    conversation_count: int
    mcp_config: Dict[str, Any]
    environment: Dict[str, str]

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the chatbot on startup."""
    global chatbot
    try:
        print("🚀 Initializing RAG + MCP Chatbot...")
        chatbot = RAGMCPChatbot()
        print("✅ Chatbot initialized successfully!")
    except Exception as e:
        print(f"❌ Failed to initialize chatbot: {e}")
        chatbot = None

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global chatbot
    if chatbot:
        await chatbot.cleanup()
        print("🧹 Chatbot cleanup completed")

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy" if chatbot else "unhealthy",
        chatbot_ready=chatbot is not None,
        mcp_server_url=os.getenv("KIOTVIET_MCP_URL", "http://localhost:8000/mcp/"),
        vector_db_path=os.getenv("VECTOR_DB_PATH", "./demo_vectordb")
    )

# System info endpoint
@app.get("/system-info", response_model=SystemInfoResponse)
async def get_system_info():
    """Get system information."""
    if not chatbot:
        raise HTTPException(status_code=503, detail="Chatbot not initialized")
    
    return SystemInfoResponse(
        chatbot_initialized=True,
        conversation_count=len(chatbot.conversation_history),
        mcp_config=chatbot.mcp_config,
        environment={
            "KIOTVIET_MCP_URL": os.getenv("KIOTVIET_MCP_URL", ""),
            "VECTOR_DB_PATH": os.getenv("VECTOR_DB_PATH", ""),
            "DEBUG": os.getenv("DEBUG", "False")
        }
    )

# Chat endpoint
@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Process chat request."""
    if not chatbot:
        raise HTTPException(status_code=503, detail="Chatbot not initialized")
    
    try:
        # Process query
        response = await chatbot.chat(request.query)
        
        # Get metadata
        metadata = {
            "max_steps": request.max_steps,
            "conversation_length": len(chatbot.conversation_history)
        }
        
        return ChatResponse(
            response=response,
            query=request.query,
            timestamp=asyncio.get_event_loop().time(),
            metadata=metadata
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat processing error: {str(e)}")

# Streaming chat endpoint
@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """Process streaming chat request."""
    if not chatbot:
        raise HTTPException(status_code=503, detail="Chatbot not initialized")
    
    async def generate_stream():
        try:
            async for chunk in chatbot.streaming_chat(request.query):
                # Format as Server-Sent Events
                data = {
                    "chunk": chunk,
                    "timestamp": asyncio.get_event_loop().time()
                }
                yield f"data: {json.dumps(data)}\n\n"
            
            # Send completion signal
            yield f"data: {json.dumps({'done': True})}\n\n"
            
        except Exception as e:
            error_data = {"error": str(e)}
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

# Get conversation history
@app.get("/conversation-history")
async def get_conversation_history():
    """Get conversation history."""
    if not chatbot:
        raise HTTPException(status_code=503, detail="Chatbot not initialized")
    
    return {
        "history": chatbot.conversation_history,
        "count": len(chatbot.conversation_history)
    }

# Clear conversation history
@app.delete("/conversation-history")
async def clear_conversation_history():
    """Clear conversation history."""
    if not chatbot:
        raise HTTPException(status_code=503, detail="Chatbot not initialized")
    
    chatbot.conversation_history.clear()
    return {"message": "Conversation history cleared"}

# Test MCP connection
@app.get("/test-mcp")
async def test_mcp_connection():
    """Test MCP server connection."""
    if not chatbot:
        raise HTTPException(status_code=503, detail="Chatbot not initialized")
    
    try:
        # Test basic MCP functionality
        result = await chatbot.mcp_agent.run("What tools do you have available?")
        
        return {
            "status": "success",
            "mcp_server_url": chatbot.mcp_config["mcpServers"]["kiotviet"]["url"],
            "test_result": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"MCP connection test failed: {str(e)}")

# Search knowledge base
@app.post("/search-knowledge")
async def search_knowledge(query: str, k: int = 3):
    """Search the RAG knowledge base."""
    if not chatbot:
        raise HTTPException(status_code=503, detail="Chatbot not initialized")
    
    try:
        context = await chatbot.rag_search(query, k=k)
        
        return {
            "query": query,
            "context": context,
            "context_length": len(context)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Knowledge search error: {str(e)}")

# Sample queries endpoint
@app.get("/sample-queries")
async def get_sample_queries():
    """Get sample queries for testing."""
    return {
        "rag_queries": [
            "Doanh thu là gì?",
            "Cách tính tồn kho?",
            "Khách hàng VIP là gì?",
            "Quy trình quản lý bán hàng"
        ],
        "mcp_queries": [
            "Lấy doanh thu tuần trước",
            "Kiểm tra tồn kho hiện tại",
            "Danh sách khách hàng VIP",
            "Thông tin các chi nhánh"
        ],
        "hybrid_queries": [
            "Phân tích doanh thu theo chi nhánh trong tháng này",
            "So sánh tồn kho với mức tồn kho lý tưởng",
            "Đánh giá hiệu quả kinh doanh của cửa hàng"
        ]
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "KiotViet RAG + MCP Chatbot API",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "chat": "/chat",
            "chat_stream": "/chat/stream",
            "system_info": "/system-info",
            "test_mcp": "/test-mcp",
            "docs": "/docs"
        },
        "status": "ready" if chatbot else "initializing"
    }

if __name__ == "__main__":
    import uvicorn
    
    # Run the server
    uvicorn.run(
        "demo_fastapi_backend:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
