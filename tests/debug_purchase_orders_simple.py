#!/usr/bin/env python3
"""Script debug đơn giản để test công cụ get_purchase_orders."""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Thêm src vào path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from albatross_kiotviet_mcp.config import get_api_client
from albatross_kiotviet_mcp.tools.get_purchase_orders_tool_impl import PurchaseOrdersToolImpl


async def test_get_purchase_orders_basic():
    """Test lấy đơn nhập hàng cơ bản."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Nhập Hàng Cơ Bản")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = PurchaseOrdersToolImpl(api_client)
        
        try:
            # <PERSON><PERSON><PERSON> cơ bản - 10 đơn nhập hàng đầu tiên
            result = await tool.execute(
                page_size=10,
                current_item=0,
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} đơn nhập hàng trong tổng số {result['total']}")
            
            # Hiển thị một vài đơn nhập hàng đầu tiên
            for i, purchase_order in enumerate(result['data'][:3]):
                print(f"\nĐơn nhập hàng {i+1}:")
                print(f"  ID: {purchase_order['id']}")
                print(f"  Mã: {purchase_order['code']}")
                print(f"  Ngày nhập: {purchase_order.get('purchaseDate', 'N/A')}")
                print(f"  Chi nhánh: {purchase_order.get('branchName', 'N/A')}")
                print(f"  Nhà cung cấp: {purchase_order.get('supplierName', 'N/A')}")
                print(f"  SĐT nhà cung cấp: {purchase_order.get('supplierContactNumber', 'N/A')}")
                print(f"  Tổng tiền: {purchase_order.get('total', 'N/A')}")
                print(f"  Đã thanh toán: {purchase_order.get('totalPayment', 'N/A')}")
                print(f"  Giảm giá: {purchase_order.get('discount', 'N/A')}")
                print(f"  Trạng thái: {purchase_order.get('status', 'N/A')} ({purchase_order.get('statusValue', 'N/A')})")
                print(f"  Mô tả: {purchase_order.get('description', 'N/A')}")
                print(f"  Người tạo: {purchase_order.get('createdBy', 'N/A')}")
                print(f"  Ngày tạo: {purchase_order.get('createdDate', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")
            print(f"Loại lỗi: {type(e).__name__}")


async def test_get_purchase_orders_by_date():
    """Test lấy đơn nhập hàng với lọc ngày."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Nhập Hàng Theo Ngày")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = PurchaseOrdersToolImpl(api_client)
        
        # Lấy đơn nhập hàng từ 30 ngày trước
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        try:
            result = await tool.execute(
                page_size=20,
                current_item=0,
                from_purchase_date=start_date.strftime("%Y-%m-%d"),
                to_purchase_date=end_date.strftime("%Y-%m-%d"),
                order_by="purchaseDate",
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} đơn nhập hàng từ {start_date.strftime('%Y-%m-%d')} đến {end_date.strftime('%Y-%m-%d')}")
            
            # Hiển thị thống kê
            if result['data']:
                total_amount = sum(purchase_order.get('total', 0) or 0 for purchase_order in result['data'])
                total_payment = sum(purchase_order.get('totalPayment', 0) or 0 for purchase_order in result['data'])
                total_discount = sum(purchase_order.get('discount', 0) or 0 for purchase_order in result['data'])
                
                print(f"Tổng giá trị đơn hàng: {total_amount:,.0f}")
                print(f"Tổng đã thanh toán: {total_payment:,.0f}")
                print(f"Tổng giảm giá: {total_discount:,.0f}")
                print(f"Còn nợ: {(total_amount - total_payment):,.0f}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_purchase_orders_by_status():
    """Test lấy đơn nhập hàng với lọc trạng thái."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Nhập Hàng Theo Trạng Thái")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = PurchaseOrdersToolImpl(api_client)
        
        # Test các giá trị trạng thái khác nhau (điều chỉnh theo hệ thống của bạn)
        statuses = [1, 2, 3]  # 1=Chờ duyệt, 2=Đã duyệt, 3=Đã hủy (có thể khác)
        
        for status in statuses:
            try:
                result = await tool.execute(
                    page_size=10,
                    current_item=0,
                    status=status,
                    order_direction="Desc"
                )
                
                status_names = {1: "Chờ duyệt", 2: "Đã duyệt", 3: "Đã hủy"}
                status_name = status_names.get(status, f"Trạng thái {status}")
                print(f"✅ Đơn nhập hàng {status_name}: {len(result['data'])} trong tổng số {result['total']}")
                
            except Exception as e:
                print(f"❌ Lỗi cho trạng thái {status}: {str(e)}")


async def test_get_purchase_orders_by_branch():
    """Test lấy đơn nhập hàng theo chi nhánh."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Nhập Hàng Theo Chi Nhánh")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = PurchaseOrdersToolImpl(api_client)
        
        try:
            # Trước tiên lấy một số đơn nhập hàng để có branch_ids
            result = await tool.execute(page_size=10, current_item=0)
            
            if result['data']:
                # Lấy branch_ids từ đơn nhập hàng đầu tiên
                branch_ids = [po['branchId'] for po in result['data'][:3] if po.get('branchId')]
                
                if branch_ids:
                    # Loại bỏ duplicates
                    branch_ids = list(set(branch_ids))
                    print(f"Test với branch IDs: {branch_ids}")
                    
                    # Lấy đơn nhập hàng theo chi nhánh cụ thể
                    branch_result = await tool.execute(
                        branch_ids=branch_ids,
                        page_size=20
                    )
                    
                    print(f"✅ Thành công: Đã lấy {len(branch_result['data'])} đơn nhập hàng cho các chi nhánh {branch_ids}")
                    
                    # Hiển thị thống kê theo chi nhánh
                    branch_stats = {}
                    for po in branch_result['data']:
                        branch_name = po.get('branchName', 'N/A')
                        if branch_name not in branch_stats:
                            branch_stats[branch_name] = {'count': 0, 'total': 0}
                        branch_stats[branch_name]['count'] += 1
                        branch_stats[branch_name]['total'] += po.get('total', 0) or 0
                    
                    print("Thống kê theo chi nhánh:")
                    for branch_name, stats in branch_stats.items():
                        print(f"  {branch_name}: {stats['count']} đơn, tổng {stats['total']:,.0f}")
                else:
                    print("⚠️  Không tìm thấy branch_ids để test")
            else:
                print("⚠️  Không tìm thấy đơn nhập hàng để test lọc chi nhánh")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_purchase_orders_with_details():
    """Test lấy đơn nhập hàng với chi tiết."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Nhập Hàng Với Chi Tiết")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = PurchaseOrdersToolImpl(api_client)
        
        try:
            result = await tool.execute(
                page_size=5,
                current_item=0,
                include_purchase_order_details=True,
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} đơn nhập hàng với chi tiết")
            
            # Hiển thị thông tin chi tiết cho đơn nhập hàng đầu tiên
            if result['data']:
                po = result['data'][0]
                print(f"\nThông tin chi tiết đơn nhập hàng:")
                print(f"  ID: {po['id']}")
                print(f"  Mã: {po['code']}")
                print(f"  Tổng tiền: {po.get('total', 'N/A')}")
                print(f"  Giảm giá: {po.get('discount', 'N/A')}")
                print(f"  Tỷ lệ giảm giá: {po.get('discountRatio', 'N/A')}")
                print(f"  Sử dụng đơn vị mới: {po.get('usingNewUnit', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_specific_purchase_orders():
    """Test lấy đơn nhập hàng cụ thể theo ID."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Nhập Hàng Cụ Thể Theo ID")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = PurchaseOrdersToolImpl(api_client)
        
        # Trước tiên lấy một số purchase order IDs
        try:
            result = await tool.execute(page_size=5, current_item=0)
            
            if result['data']:
                # Lấy IDs từ đơn nhập hàng đầu tiên
                po_ids = [po['id'] for po in result['data'][:3]]
                print(f"Test với purchase order IDs: {po_ids}")
                
                # Lấy đơn nhập hàng cụ thể
                specific_result = await tool.execute(ids=po_ids)
                
                print(f"✅ Thành công: Đã lấy {len(specific_result['data'])} đơn nhập hàng cụ thể")
                
                for po in specific_result['data']:
                    print(f"  Đơn {po['id']}: {po['code']} - {po.get('supplierName', 'N/A')} - {po.get('total', 'N/A')}")
            else:
                print("⚠️  Không tìm thấy đơn nhập hàng để test lấy ID cụ thể")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


if __name__ == "__main__":
    print("🚀 Bắt đầu test debug công cụ get_purchase_orders...")
    
    asyncio.run(test_get_purchase_orders_basic())
    asyncio.run(test_get_purchase_orders_by_date())
    asyncio.run(test_get_purchase_orders_by_status())
    asyncio.run(test_get_purchase_orders_by_branch())
    asyncio.run(test_get_purchase_orders_with_details())
    asyncio.run(test_get_specific_purchase_orders())
    
    print("\n✅ Hoàn thành test debug!")