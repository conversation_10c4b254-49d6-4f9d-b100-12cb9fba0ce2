#!/usr/bin/env python3
"""Script debug đơn giản để test công cụ get_cashflow."""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Thêm src vào path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from albatross_kiotviet_mcp.config import get_api_client
from albatross_kiotviet_mcp.tools.get_cashflow_tool_impl import CashFlowToolImpl


async def test_get_cashflow_basic():
    """Test lấy phiếu thu chi cơ bản."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Phiếu Thu Chi Cơ Bản")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CashFlowToolImpl(api_client)
        
        try:
            # L<PERSON><PERSON> cơ bản - 10 phiếu thu chi đầu tiên
            result = await tool.execute(
                page_size=10,
                current_item=0,
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} phiếu thu chi trong tổng số {result['total']}")
            
            # Hiển thị một vài phiếu thu chi đầu tiên
            for i, cashflow in enumerate(result['data'][:3]):
                print(f"\nPhiếu thu chi {i+1}:")
                print(f"  ID: {cashflow['id']}")
                print(f"  Mã: {cashflow['code']}")
                print(f"  Ngày giao dịch: {cashflow.get('transactionDate', 'N/A')}")
                print(f"  Chi nhánh: {cashflow.get('branchName', 'N/A')}")
                print(f"  Loại: {cashflow.get('type', 'N/A')} ({cashflow.get('typeValue', 'N/A')})")
                print(f"  Số tiền: {cashflow.get('amount', 'N/A')}")
                print(f"  Mô tả: {cashflow.get('description', 'N/A')}")
                print(f"  Tham chiếu: {cashflow.get('reference', 'N/A')}")
                print(f"  Khách hàng: {cashflow.get('customerName', 'N/A')} ({cashflow.get('customerCode', 'N/A')})")
                print(f"  Nhà cung cấp: {cashflow.get('supplierName', 'N/A')}")
                print(f"  Hóa đơn: {cashflow.get('invoiceCode', 'N/A')}")
                print(f"  Đơn nhập hàng: {cashflow.get('purchaseOrderCode', 'N/A')}")
                print(f"  Người tạo: {cashflow.get('createdBy', 'N/A')}")
                print(f"  Ngày tạo: {cashflow.get('createdDate', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")
            print(f"Loại lỗi: {type(e).__name__}")


async def test_get_cashflow_by_date():
    """Test lấy phiếu thu chi với lọc ngày."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Phiếu Thu Chi Theo Ngày")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CashFlowToolImpl(api_client)
        
        # Lấy phiếu thu chi từ 30 ngày trước
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        try:
            result = await tool.execute(
                page_size=20,
                current_item=0,
                from_transaction_date=start_date.strftime("%Y-%m-%d"),
                to_transaction_date=end_date.strftime("%Y-%m-%d"),
                order_by="transactionDate",
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} phiếu thu chi từ {start_date.strftime('%Y-%m-%d')} đến {end_date.strftime('%Y-%m-%d')}")
            
            # Hiển thị thống kê
            if result['data']:
                total_income = sum(cf.get('amount', 0) or 0 for cf in result['data'] if cf.get('type') == 1)
                total_expense = sum(cf.get('amount', 0) or 0 for cf in result['data'] if cf.get('type') == 2)
                net_cashflow = total_income - total_expense
                
                print(f"Tổng thu: {total_income:,.0f}")
                print(f"Tổng chi: {total_expense:,.0f}")
                print(f"Dòng tiền ròng: {net_cashflow:,.0f}")
                
                # Thống kê theo loại giao dịch
                type_stats = {}
                for cf in result['data']:
                    type_value = cf.get('typeValue', 'N/A')
                    if type_value not in type_stats:
                        type_stats[type_value] = {'count': 0, 'amount': 0}
                    type_stats[type_value]['count'] += 1
                    type_stats[type_value]['amount'] += cf.get('amount', 0) or 0
                
                print("Thống kê theo loại giao dịch:")
                for type_name, stats in type_stats.items():
                    print(f"  {type_name}: {stats['count']} giao dịch, tổng {stats['amount']:,.0f}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_cashflow_by_type():
    """Test lấy phiếu thu chi với lọc loại giao dịch."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Phiếu Thu Chi Theo Loại Giao Dịch")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CashFlowToolImpl(api_client)
        
        # Test các loại giao dịch khác nhau
        transaction_types = [1, 2]  # 1=Thu, 2=Chi
        
        for trans_type in transaction_types:
            try:
                result = await tool.execute(
                    page_size=10,
                    current_item=0,
                    transaction_type=trans_type,
                    order_direction="Desc"
                )
                
                type_name = "Thu" if trans_type == 1 else "Chi"
                print(f"✅ Phiếu {type_name}: {len(result['data'])} trong tổng số {result['total']}")
                
                if result['data']:
                    total_amount = sum(cf.get('amount', 0) or 0 for cf in result['data'])
                    print(f"   Tổng số tiền: {total_amount:,.0f}")
                
            except Exception as e:
                print(f"❌ Lỗi cho loại giao dịch {trans_type}: {str(e)}")


async def test_get_cashflow_by_branch():
    """Test lấy phiếu thu chi theo chi nhánh."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Phiếu Thu Chi Theo Chi Nhánh")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CashFlowToolImpl(api_client)
        
        try:
            # Trước tiên lấy một số phiếu thu chi để có branch_ids
            result = await tool.execute(page_size=10, current_item=0)
            
            if result['data']:
                # Lấy branch_ids từ phiếu thu chi đầu tiên
                branch_ids = [cf['branchId'] for cf in result['data'][:3] if cf.get('branchId')]
                
                if branch_ids:
                    # Loại bỏ duplicates
                    branch_ids = list(set(branch_ids))
                    print(f"Test với branch IDs: {branch_ids}")
                    
                    # Lấy phiếu thu chi theo chi nhánh cụ thể
                    branch_result = await tool.execute(
                        branch_ids=branch_ids,
                        page_size=20
                    )
                    
                    print(f"✅ Thành công: Đã lấy {len(branch_result['data'])} phiếu thu chi cho các chi nhánh {branch_ids}")
                    
                    # Hiển thị thống kê theo chi nhánh
                    branch_stats = {}
                    for cf in branch_result['data']:
                        branch_name = cf.get('branchName', 'N/A')
                        if branch_name not in branch_stats:
                            branch_stats[branch_name] = {'count': 0, 'income': 0, 'expense': 0}
                        branch_stats[branch_name]['count'] += 1
                        amount = cf.get('amount', 0) or 0
                        if cf.get('type') == 1:  # Thu
                            branch_stats[branch_name]['income'] += amount
                        elif cf.get('type') == 2:  # Chi
                            branch_stats[branch_name]['expense'] += amount
                    
                    print("Thống kê theo chi nhánh:")
                    for branch_name, stats in branch_stats.items():
                        net = stats['income'] - stats['expense']
                        print(f"  {branch_name}: {stats['count']} giao dịch, Thu: {stats['income']:,.0f}, Chi: {stats['expense']:,.0f}, Ròng: {net:,.0f}")
                else:
                    print("⚠️  Không tìm thấy branch_ids để test")
            else:
                print("⚠️  Không tìm thấy phiếu thu chi để test lọc chi nhánh")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_cashflow_by_customer():
    """Test lấy phiếu thu chi theo khách hàng."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Phiếu Thu Chi Theo Khách Hàng")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CashFlowToolImpl(api_client)
        
        try:
            # Trước tiên lấy một số phiếu thu chi để có customer_ids
            result = await tool.execute(page_size=20, current_item=0)
            
            if result['data']:
                # Lấy customer_ids từ phiếu thu chi có khách hàng
                customer_ids = [cf['customerId'] for cf in result['data'] if cf.get('customerId')]
                
                if customer_ids:
                    # Loại bỏ duplicates và lấy 3 đầu tiên
                    customer_ids = list(set(customer_ids))[:3]
                    print(f"Test với customer IDs: {customer_ids}")
                    
                    # Lấy phiếu thu chi theo khách hàng cụ thể
                    customer_result = await tool.execute(
                        customer_ids=customer_ids,
                        page_size=20
                    )
                    
                    print(f"✅ Thành công: Đã lấy {len(customer_result['data'])} phiếu thu chi cho các khách hàng {customer_ids}")
                    
                    # Hiển thị thông tin chi tiết
                    for cf in customer_result['data'][:5]:
                        type_name = "Thu" if cf.get('type') == 1 else "Chi" if cf.get('type') == 2 else "Khác"
                        print(f"  {cf['code']}: {cf.get('customerName', 'N/A')} - {type_name} {cf.get('amount', 'N/A')}")
                else:
                    print("⚠️  Không tìm thấy customer_ids để test")
            else:
                print("⚠️  Không tìm thấy phiếu thu chi để test lọc khách hàng")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_cashflow_by_invoice():
    """Test lấy phiếu thu chi theo hóa đơn."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Phiếu Thu Chi Theo Hóa Đơn")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CashFlowToolImpl(api_client)
        
        try:
            # Trước tiên lấy một số phiếu thu chi để có invoice_ids
            result = await tool.execute(page_size=20, current_item=0)
            
            if result['data']:
                # Lấy invoice_ids từ phiếu thu chi có hóa đơn
                invoice_ids = [cf['invoiceId'] for cf in result['data'] if cf.get('invoiceId')]
                
                if invoice_ids:
                    # Loại bỏ duplicates và lấy 3 đầu tiên
                    invoice_ids = list(set(invoice_ids))[:3]
                    print(f"Test với invoice IDs: {invoice_ids}")
                    
                    # Lấy phiếu thu chi theo hóa đơn cụ thể
                    invoice_result = await tool.execute(
                        invoice_ids=invoice_ids,
                        page_size=20
                    )
                    
                    print(f"✅ Thành công: Đã lấy {len(invoice_result['data'])} phiếu thu chi cho các hóa đơn {invoice_ids}")
                    
                    # Hiển thị thông tin chi tiết
                    for cf in invoice_result['data']:
                        type_name = "Thu" if cf.get('type') == 1 else "Chi" if cf.get('type') == 2 else "Khác"
                        print(f"  {cf['code']}: Hóa đơn {cf.get('invoiceCode', 'N/A')} - {type_name} {cf.get('amount', 'N/A')}")
                else:
                    print("⚠️  Không tìm thấy invoice_ids để test")
            else:
                print("⚠️  Không tìm thấy phiếu thu chi để test lọc hóa đơn")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_specific_cashflow():
    """Test lấy phiếu thu chi cụ thể theo ID."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Phiếu Thu Chi Cụ Thể Theo ID")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CashFlowToolImpl(api_client)
        
        # Trước tiên lấy một số cashflow IDs
        try:
            result = await tool.execute(page_size=5, current_item=0)
            
            if result['data']:
                # Lấy IDs từ phiếu thu chi đầu tiên
                cashflow_ids = [cf['id'] for cf in result['data'][:3]]
                print(f"Test với cashflow IDs: {cashflow_ids}")
                
                # Lấy phiếu thu chi cụ thể
                specific_result = await tool.execute(ids=cashflow_ids)
                
                print(f"✅ Thành công: Đã lấy {len(specific_result['data'])} phiếu thu chi cụ thể")
                
                for cf in specific_result['data']:
                    type_name = "Thu" if cf.get('type') == 1 else "Chi" if cf.get('type') == 2 else "Khác"
                    print(f"  Phiếu {cf['id']}: {cf['code']} - {type_name} {cf.get('amount', 'N/A')} - {cf.get('description', 'N/A')}")
            else:
                print("⚠️  Không tìm thấy phiếu thu chi để test lấy ID cụ thể")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


if __name__ == "__main__":
    print("🚀 Bắt đầu test debug công cụ get_cashflow...")
    
    asyncio.run(test_get_cashflow_basic())
    asyncio.run(test_get_cashflow_by_date())
    asyncio.run(test_get_cashflow_by_type())
    asyncio.run(test_get_cashflow_by_branch())
    asyncio.run(test_get_cashflow_by_customer())
    asyncio.run(test_get_cashflow_by_invoice())
    asyncio.run(test_get_specific_cashflow())
    
    print("\n✅ Hoàn thành test debug!")