#!/usr/bin/env python3
"""Simple debug script for testing get_product_by_code tool."""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from albatross_kiotviet_mcp.config import get_api_client
from albatross_kiotviet_mcp.tools.get_product_by_code_tool_impl import ProductByCodeToolImpl


async def test_get_product_by_code():
    """Test the get_product_by_code tool with sample data."""
    
    # Test product codes - replace with actual codes from your KiotViet system
    test_codes = [
        "SP001",  # Replace with actual product code
        "PRODUCT-123",  # Replace with actual product code
        "NONEXISTENT",  # This should return not found
    ]
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ProductByCodeToolImpl(api_client)
        
        for code in test_codes:
            print(f"\n{'='*50}")
            print(f"Testing product code: {code}")
            print(f"{'='*50}")
            
            try:
                result = await tool.execute(code=code)
                
                if result["success"]:
                    product = result["product"]
                    print(f"✅ Success: {result['message']}")
                    print(f"Product ID: {product['id']}")
                    print(f"Product Name: {product['name']}")
                    print(f"Product Code: {product['code']}")
                    print(f"Category: {product.get('categoryName', 'N/A')}")
                    print(f"Base Price: {product.get('basePrice', 'N/A')}")
                    print(f"Is Active: {product.get('isActive', 'N/A')}")
                    print(f"Allows Sale: {product.get('allowsSale', 'N/A')}")
                else:
                    print(f"❌ Error: {result['error']}")
                    print(f"Error Type: {result.get('error_type', 'unknown')}")
                    
            except Exception as e:
                print(f"❌ Exception occurred: {str(e)}")
                print(f"Exception type: {type(e).__name__}")


async def test_edge_cases():
    """Test edge cases and validation."""
    
    print(f"\n{'='*50}")
    print("Testing Edge Cases")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ProductByCodeToolImpl(api_client)
        
        # Test empty code
        try:
            result = await tool.execute(code="")
            print("❌ Empty code should have failed")
        except ValueError as e:
            print(f"✅ Empty code validation: {str(e)}")
        
        # Test whitespace only
        try:
            result = await tool.execute(code="   ")
            print("❌ Whitespace code should have failed")
        except ValueError as e:
            print(f"✅ Whitespace code validation: {str(e)}")
        
        # Test code with special characters
        try:
            result = await tool.execute(code="SP-001/TEST")
            print(f"✅ Special characters handled")
        except Exception as e:
            print(f"⚠️  Special characters error: {str(e)}")


if __name__ == "__main__":
    print("🚀 Starting get_product_by_code tool debug test...")
    print("📝 Note: Update test_codes with actual product codes from your KiotViet system")
    
    asyncio.run(test_get_product_by_code())
    asyncio.run(test_edge_cases())
    
    print("\n✅ Debug test completed!")