#!/usr/bin/env python3
"""
Simple debug script cho Revenue Calculation Tool.
G<PERSON>i trực tiếp vào execute() method để debug revenue calculation logic.

Usage:
    python tests/debug_revenue_simple.py
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from albatross_kiotviet_mcp.config.config import Settings
from albatross_kiotviet_mcp.api.api_client import KiotVietAPIClient
from albatross_kiotviet_mcp.tools.calculate_daily_revenue_tool_impl import DailyRevenueToolImpl


async def debug_revenue_calculation():
    """Debug revenue calculation step by step."""
    
    # Setup
    settings = Settings(
        kiotviet_client_id=os.getenv("KIOTVIET_CLIENT_ID", "test_client_id"),
        kiotviet_client_secret=os.getenv("KIOTVIET_CLIENT_SECRET", "test_client_secret"),
        kiotviet_retailer=os.getenv("KIOTVIET_RETAILER", "test_retailer"),
    )
    
    print("💰 Debugging Revenue Calculation Tool")
    print(f"Retailer: {settings.kiotviet_retailer}")
    print("-" * 50)
    
    # Create API client
    api_client = KiotVietAPIClient(settings)
    
    try:
        async with api_client:
            # Create tool instance
            tool = DailyRevenueToolImpl(api_client)
            
            # Test với ngày hôm nay
            today = datetime.now().strftime("%Y-%m-%d")
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            
            print("📋 Calling tool.execute()...")
            print("   Parameters:")
            print(f"   - from_date: {yesterday}")
            print(f"   - to_date: {today}")
            print("   - include_details: True")
            print()
            print("⏳ This will fetch all invoices and calculate revenue...")
            print("   You can debug the pagination logic and calculations")
            print()
            
            # 🚨 SET BREAKPOINT HERE để debug vào execute() method
            # Đặc biệt useful để debug:
            # - Pagination logic (while loop)
            # - Revenue calculation logic
            # - Invoice processing
            result = await tool.execute(
                from_date=yesterday,
                to_date=today,
                include_details=True
            )
            
            print("✅ Revenue calculation completed!")
            print(f"Result type: {type(result)}")
            print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            if isinstance(result, dict):
                # Print date range
                date_range = result.get('dateRange', {})
                print(f"\n📅 Date Range:")
                print(f"   From: {date_range.get('fromDate', 'N/A')}")
                print(f"   To: {date_range.get('toDate', 'N/A')}")
                
                # Print summary
                summary = result.get('summary', {})
                print(f"\n📊 Revenue Summary:")
                print(f"   Total Revenue: {summary.get('totalRevenue', 0):,} VND")
                print(f"   Total Invoices: {summary.get('totalInvoices', 0)}")
                print(f"   Average per Invoice: {summary.get('averageRevenuePerInvoice', 0):,} VND")
                
                # Print invoice details
                details = result.get('invoiceDetails', [])
                if details:
                    print(f"\n📋 Invoice Details ({len(details)} invoices):")
                    for i, invoice in enumerate(details[:5]):  # Show first 5
                        print(f"   {i+1}. {invoice.get('code', 'N/A')}")
                        print(f"      Amount: {invoice.get('total', 0):,} VND")
                        print(f"      Customer: {invoice.get('customer', 'N/A')}")
                        print(f"      Date: {invoice.get('purchaseDate', 'N/A')}")
                    
                    if len(details) > 5:
                        print(f"   ... and {len(details) - 5} more invoices")
                else:
                    print("\n📋 No invoice details (include_details was False or no invoices found)")
            
            return result
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Print full traceback for debugging
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
        
        return None


async def debug_revenue_step_by_step():
    """Debug revenue calculation với detailed step-by-step output."""
    
    settings = Settings(
        kiotviet_client_id=os.getenv("KIOTVIET_CLIENT_ID", "test_client_id"),
        kiotviet_client_secret=os.getenv("KIOTVIET_CLIENT_SECRET", "test_client_secret"),
        kiotviet_retailer=os.getenv("KIOTVIET_RETAILER", "test_retailer"),
    )
    
    api_client = KiotVietAPIClient(settings)
    
    try:
        async with api_client:
            tool = DailyRevenueToolImpl(api_client)
            
            today = datetime.now().strftime("%Y-%m-%d")
            
            print("🔍 Step-by-step revenue calculation debug:")
            print(f"   Date: {today}")
            print("   Set breakpoints in execute() method to see:")
            print("   1. Invoice fetching loop")
            print("   2. Pagination logic")
            print("   3. Revenue calculation")
            print("   4. Result building")
            print()
            
            # 🚨 PERFECT PLACE để set breakpoint và step through
            result = await tool.execute(
                from_date=today,
                to_date=today,
                include_details=False  # Faster for debugging
            )
            
            return result
            
    except Exception as e:
        print(f"❌ Step-by-step debug error: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    print("🚀 Revenue Calculation Tool Debug Script")
    print("=" * 50)
    
    # Check environment variables
    if not os.getenv("KIOTVIET_CLIENT_ID"):
        print("⚠️  Set KIOTVIET_CLIENT_ID environment variable for real API testing")
    if not os.getenv("KIOTVIET_CLIENT_SECRET"):
        print("⚠️  Set KIOTVIET_CLIENT_SECRET environment variable for real API testing")
    if not os.getenv("KIOTVIET_RETAILER"):
        print("⚠️  Set KIOTVIET_RETAILER environment variable for real API testing")
    
    print()
    
    # Choose debug mode
    print("Select debug mode:")
    print("1. Full revenue calculation with details")
    print("2. Step-by-step debugging (faster)")
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            result = asyncio.run(debug_revenue_calculation())
        elif choice == "2":
            result = asyncio.run(debug_revenue_step_by_step())
        else:
            print("Invalid choice, running full calculation...")
            result = asyncio.run(debug_revenue_calculation())
        
        if result:
            print("\n🎉 Debug completed successfully!")
        else:
            print("\n💥 Debug failed - check errors above")
            
    except KeyboardInterrupt:
        print("\n👋 Debug interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")