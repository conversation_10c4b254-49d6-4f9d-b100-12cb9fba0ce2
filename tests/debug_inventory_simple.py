#!/usr/bin/env python3
"""
Simple debug script cho Inventory Tool.
G<PERSON>i trực tiếp vào execute() method để debug inventory logic.

Usage:
    python tests/debug_inventory_simple.py
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from albatross_kiotviet_mcp.config.config import Settings
from albatross_kiotviet_mcp.api.api_client import KiotVietAPIClient
from albatross_kiotviet_mcp.tools.get_inventory_tool_impl import InventoryToolImpl


async def debug_inventory():
    """Debug inventory tool execution step by step."""
    
    # Setup
    settings = Settings(
        kiotviet_client_id=os.getenv("KIOTVIET_CLIENT_ID", "test_client_id"),
        kiotviet_client_secret=os.getenv("KIOTVIET_CLIENT_SECRET", "test_client_secret"),
        kiotviet_retailer=os.getenv("KIOTVIET_RETAILER", "test_retailer"),
    )
    
    print("📦 Debugging Inventory Tool")
    print(f"Retailer: {settings.kiotviet_retailer}")
    print("-" * 50)
    
    # Create API client
    api_client = KiotVietAPIClient(settings)
    
    try:
        async with api_client:
            # Create tool instance
            tool = InventoryToolImpl(api_client)
            
            print("📋 Calling tool.execute()...")
            print("   Parameters:")
            print("   - page_size: 10")
            print("   - current_item: 0")
            print("   - order_by: Code")
            print("   - last_modified_from: None")
            print("   - branch_ids: [1] (filter by branch 1)")
            print()
            
            # 🚨 SET BREAKPOINT HERE để debug vào execute() method
            result = await tool.execute(
                page_size=10,
                current_item=0,
                order_by="Code",
                last_modified_from=None,
                branch_ids=[1]
            )
            
            print("✅ Execution completed!")
            print(f"Result type: {type(result)}")
            print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            if isinstance(result, dict):
                print(f"Total products: {result.get('total', 'N/A')}")
                print(f"Page size: {result.get('pageSize', 'N/A')}")
                
                data = result.get('data', [])
                print(f"Products returned: {len(data)}")
                
                if data:
                    print("\nInventory details:")
                    for i, product in enumerate(data):
                        print(f"  {i+1}. {product.get('name', 'N/A')} (Code: {product.get('code', 'N/A')})")
                        print(f"     On Hand: {product.get('onHand', 0)}")
                        print(f"     Reserved: {product.get('reserved', 0)}")
                        print(f"     Available: {product.get('available', 0)}")
                        print(f"     Branch: {product.get('branchName', 'N/A')}")
                        print()
            
            return result
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Print full traceback for debugging
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
        
        return None


async def debug_inventory_with_filters():
    """Debug inventory với various filters."""
    
    settings = Settings(
        kiotviet_client_id=os.getenv("KIOTVIET_CLIENT_ID", "test_client_id"),
        kiotviet_client_secret=os.getenv("KIOTVIET_CLIENT_SECRET", "test_client_secret"),
        kiotviet_retailer=os.getenv("KIOTVIET_RETAILER", "test_retailer"),
    )
    
    api_client = KiotVietAPIClient(settings)
    
    try:
        async with api_client:
            tool = InventoryToolImpl(api_client)
            
            # Test với datetime filter
            last_week = datetime.now() - timedelta(days=7)
            
            print("🔍 Testing inventory with filters:")
            print(f"   last_modified_from: {last_week}")
            print("   order_by: Name")
            print("   branch_ids: [1, 2]")
            print()
            
            # 🚨 SET BREAKPOINT HERE để debug filtering logic
            result = await tool.execute(
                page_size=5,
                current_item=0,
                order_by="Name",
                last_modified_from=last_week,
                branch_ids=[1, 2]
            )
            
            print("✅ Filtered inventory result:")
            if isinstance(result, dict):
                data = result.get('data', [])
                print(f"   Found {len(data)} products matching filters")
                
                for product in data:
                    print(f"   - {product.get('name', 'N/A')}")
                    print(f"     Stock: {product.get('onHand', 0)} (Available: {product.get('available', 0)})")
            
            return result
            
    except Exception as e:
        print(f"❌ Filtered inventory error: {e}")
        import traceback
        traceback.print_exc()
        return None


async def debug_inventory_validation():
    """Debug inventory parameter validation."""
    
    settings = Settings(
        kiotviet_client_id=os.getenv("KIOTVIET_CLIENT_ID", "test_client_id"),
        kiotviet_client_secret=os.getenv("KIOTVIET_CLIENT_SECRET", "test_client_secret"),
        kiotviet_retailer=os.getenv("KIOTVIET_RETAILER", "test_retailer"),
    )
    
    api_client = KiotVietAPIClient(settings)
    
    try:
        async with api_client:
            tool = InventoryToolImpl(api_client)
            
            print("🔧 Testing parameter validation:")
            
            # Test invalid page_size
            try:
                await tool.execute(page_size=101)
                print("❌ Should have failed with page_size=101")
            except ValueError as e:
                print(f"✅ Correctly caught: {e}")
            
            # Test invalid current_item
            try:
                await tool.execute(current_item=-1)
                print("❌ Should have failed with current_item=-1")
            except ValueError as e:
                print(f"✅ Correctly caught: {e}")
            
            print("✅ Validation tests completed!")
            
    except Exception as e:
        print(f"❌ Validation test error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 Inventory Tool Debug Script")
    print("=" * 50)
    
    # Check environment variables
    if not os.getenv("KIOTVIET_CLIENT_ID"):
        print("⚠️  Set KIOTVIET_CLIENT_ID environment variable for real API testing")
    if not os.getenv("KIOTVIET_CLIENT_SECRET"):
        print("⚠️  Set KIOTVIET_CLIENT_SECRET environment variable for real API testing")
    if not os.getenv("KIOTVIET_RETAILER"):
        print("⚠️  Set KIOTVIET_RETAILER environment variable for real API testing")
    
    print()
    
    # Choose debug mode
    print("Select debug mode:")
    print("1. Basic inventory query")
    print("2. Inventory with filters")
    print("3. Parameter validation")
    
    try:
        choice = input("Enter choice (1, 2, or 3): ").strip()
        
        if choice == "1":
            result = asyncio.run(debug_inventory())
        elif choice == "2":
            result = asyncio.run(debug_inventory_with_filters())
        elif choice == "3":
            asyncio.run(debug_inventory_validation())
            result = True
        else:
            print("Invalid choice, running basic inventory query...")
            result = asyncio.run(debug_inventory())
        
        if result:
            print("\n🎉 Debug completed successfully!")
        else:
            print("\n💥 Debug failed - check errors above")
            
    except KeyboardInterrupt:
        print("\n👋 Debug interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")