#!/usr/bin/env python3
"""Script debug đơn giản để test công cụ get_customers."""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Thêm src vào path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from albatross_kiotviet_mcp.config import get_api_client
from albatross_kiotviet_mcp.tools.get_customers_tool_impl import CustomersToolImpl


async def test_get_customers_basic():
    """Test lấy khách hàng cơ bản."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Khách Hàng Cơ Bản")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CustomersToolImpl(api_client)
        
        try:
            # L<PERSON><PERSON> cơ bản - 10 khách hàng đầu tiên
            result = await tool.execute(
                page_size=10,
                current_item=0,
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đ<PERSON> lấy {len(result['data'])} khách hàng trong tổng số {result['total']}")
            
            # Hiển thị một vài khách hàng đầu tiên
            for i, customer in enumerate(result['data'][:3]):
                print(f"\nKhách hàng {i+1}:")
                print(f"  ID: {customer['id']}")
                print(f"  Mã: {customer['code']}")
                print(f"  Tên: {customer['name']}")
                print(f"  Số điện thoại: {customer.get('contactNumber', 'N/A')}")
                print(f"  Địa chỉ: {customer.get('address', 'N/A')}")
                print(f"  Chi nhánh: {customer.get('branchName', 'N/A')}")
                print(f"  Nhóm: {customer.get('groupName', 'N/A')}")
                print(f"  Công nợ: {customer.get('debt', 'N/A')}")
                print(f"  Tổng hóa đơn: {customer.get('totalInvoiced', 'N/A')}")
                print(f"  Điểm: {customer.get('totalPoint', 'N/A')}")
                print(f"  Hoạt động: {customer.get('isActive', 'N/A')}")
                print(f"  Ngày tạo: {customer.get('createdDate', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")
            print(f"Loại lỗi: {type(e).__name__}")


async def test_get_customers_by_date():
    """Test lấy khách hàng với lọc ngày."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Khách Hàng Theo Ngày")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CustomersToolImpl(api_client)
        
        # Lấy khách hàng từ 30 ngày trước
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        try:
            result = await tool.execute(
                page_size=20,
                current_item=0,
                from_created_date=start_date.strftime("%Y-%m-%d"),
                to_created_date=end_date.strftime("%Y-%m-%d"),
                order_by="createdDate",
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} khách hàng từ {start_date.strftime('%Y-%m-%d')} đến {end_date.strftime('%Y-%m-%d')}")
            
            # Hiển thị thống kê
            if result['data']:
                active_customers = sum(1 for customer in result['data'] if customer.get('isActive', False))
                total_debt = sum(customer.get('debt', 0) or 0 for customer in result['data'])
                total_invoiced = sum(customer.get('totalInvoiced', 0) or 0 for customer in result['data'])
                
                print(f"Khách hàng hoạt động: {active_customers}/{len(result['data'])}")
                print(f"Tổng công nợ: {total_debt:,.0f}")
                print(f"Tổng giá trị hóa đơn: {total_invoiced:,.0f}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_customers_by_status():
    """Test lấy khách hàng với lọc trạng thái."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Khách Hàng Theo Trạng Thái")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CustomersToolImpl(api_client)
        
        # Test các giá trị trạng thái khác nhau
        statuses = [True, False]  # True=Hoạt động, False=Không hoạt động
        
        for is_active in statuses:
            try:
                result = await tool.execute(
                    page_size=10,
                    current_item=0,
                    is_active=is_active,
                    order_direction="Desc"
                )
                
                status_name = "Hoạt động" if is_active else "Không hoạt động"
                print(f"✅ Khách hàng {status_name}: {len(result['data'])} trong tổng số {result['total']}")
                
            except Exception as e:
                print(f"❌ Lỗi cho trạng thái {is_active}: {str(e)}")


async def test_get_customers_by_branch():
    """Test lấy khách hàng theo chi nhánh."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Khách Hàng Theo Chi Nhánh")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CustomersToolImpl(api_client)
        
        try:
            # Trước tiên lấy một số khách hàng để có branch_ids
            result = await tool.execute(page_size=10, current_item=0)
            
            if result['data']:
                # Lấy branch_ids từ khách hàng đầu tiên
                branch_ids = [customer['branchId'] for customer in result['data'][:3] if customer.get('branchId')]
                
                if branch_ids:
                    # Loại bỏ duplicates
                    branch_ids = list(set(branch_ids))
                    print(f"Test với branch IDs: {branch_ids}")
                    
                    # Lấy khách hàng theo chi nhánh cụ thể
                    branch_result = await tool.execute(
                        branch_ids=branch_ids,
                        page_size=20
                    )
                    
                    print(f"✅ Thành công: Đã lấy {len(branch_result['data'])} khách hàng cho các chi nhánh {branch_ids}")
                    
                    # Hiển thị thống kê theo chi nhánh
                    branch_stats = {}
                    for customer in branch_result['data']:
                        branch_name = customer.get('branchName', 'N/A')
                        if branch_name not in branch_stats:
                            branch_stats[branch_name] = 0
                        branch_stats[branch_name] += 1
                    
                    print("Thống kê theo chi nhánh:")
                    for branch_name, count in branch_stats.items():
                        print(f"  {branch_name}: {count} khách hàng")
                else:
                    print("⚠️  Không tìm thấy branch_ids để test")
            else:
                print("⚠️  Không tìm thấy khách hàng để test lọc chi nhánh")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_specific_customers():
    """Test lấy khách hàng cụ thể theo ID."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Khách Hàng Cụ Thể Theo ID")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = CustomersToolImpl(api_client)
        
        # Trước tiên lấy một số customer IDs
        try:
            result = await tool.execute(page_size=5, current_item=0)
            
            if result['data']:
                # Lấy IDs từ khách hàng đầu tiên
                customer_ids = [customer['id'] for customer in result['data'][:3]]
                print(f"Test với customer IDs: {customer_ids}")
                
                # Lấy khách hàng cụ thể
                specific_result = await tool.execute(ids=customer_ids)
                
                print(f"✅ Thành công: Đã lấy {len(specific_result['data'])} khách hàng cụ thể")
                
                for customer in specific_result['data']:
                    print(f"  Khách hàng {customer['id']}: {customer['name']} - {customer.get('contactNumber', 'N/A')}")
            else:
                print("⚠️  Không tìm thấy khách hàng để test lấy ID cụ thể")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


if __name__ == "__main__":
    print("🚀 Bắt đầu test debug công cụ get_customers...")
    
    asyncio.run(test_get_customers_basic())
    asyncio.run(test_get_customers_by_date())
    asyncio.run(test_get_customers_by_status())
    asyncio.run(test_get_customers_by_branch())
    asyncio.run(test_get_specific_customers())
    
    print("\n✅ Hoàn thành test debug!")