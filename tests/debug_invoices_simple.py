#!/usr/bin/env python3
"""Simple debug script for testing get_invoices tool."""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from albatross_kiotviet_mcp.config import get_api_client
from albatross_kiotviet_mcp.tools.get_invoices_tool_impl import InvoicesToolImpl


async def test_get_invoices_basic():
    """Test basic invoice retrieval."""
    
    print(f"\n{'='*50}")
    print("Testing Basic Invoice Retrieval")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = InvoicesToolImpl(api_client)
        
        try:
            # Basic retrieval - first 10 invoices
            result = await tool.execute(
                page_size=10,
                current_item=0,
                order_direction="Desc"
            )
            
            print(f"✅ Success: Retrieved {len(result['data'])} invoices out of {result['total']} total")
            
            # Display first few invoices
            for i, invoice in enumerate(result['data'][:3]):
                print(f"\nInvoice {i+1}:")
                print(f"  ID: {invoice['id']}")
                print(f"  Code: {invoice['code']}")
                print(f"  Total: {invoice.get('total', 'N/A')}")
                print(f"  Customer: {invoice.get('customerName', 'N/A')}")
                print(f"  Branch: {invoice.get('branchName', 'N/A')}")
                print(f"  Status: {invoice.get('status', 'N/A')}")
                print(f"  Created: {invoice.get('createdDate', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Exception occurred: {str(e)}")
            print(f"Exception type: {type(e).__name__}")


async def test_get_invoices_by_date():
    """Test invoice retrieval with date filtering."""
    
    print(f"\n{'='*50}")
    print("Testing Invoice Retrieval by Date")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = InvoicesToolImpl(api_client)
        
        # Get invoices from last 7 days
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        try:
            result = await tool.execute(
                page_size=20,
                current_item=0,
                from_purchase_date=start_date.strftime("%Y-%m-%d"),
                to_purchase_date=end_date.strftime("%Y-%m-%d"),
                order_by="createdDate",
                order_direction="Desc"
            )
            
            print(f"✅ Success: Retrieved {len(result['data'])} invoices from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            
            # Display summary
            if result['data']:
                total_amount = sum(invoice.get('total', 0) or 0 for invoice in result['data'])
                print(f"Total Amount: {total_amount:,.0f}")
                print(f"Average Amount: {total_amount / len(result['data']):,.0f}")
                
        except Exception as e:
            print(f"❌ Exception occurred: {str(e)}")


async def test_get_invoices_by_status():
    """Test invoice retrieval with status filtering."""
    
    print(f"\n{'='*50}")
    print("Testing Invoice Retrieval by Status")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = InvoicesToolImpl(api_client)
        
        # Test different status values
        statuses = [1, 2]  # 1=Active, 2=Cancelled (adjust based on your system)
        
        for status in statuses:
            try:
                result = await tool.execute(
                    page_size=10,
                    current_item=0,
                    status=status,
                    order_direction="Desc"
                )
                
                status_name = "Active" if status == 1 else "Cancelled" if status == 2 else f"Status {status}"
                print(f"✅ {status_name} invoices: {len(result['data'])} out of {result['total']} total")
                
            except Exception as e:
                print(f"❌ Error for status {status}: {str(e)}")


async def test_get_invoices_with_details():
    """Test invoice retrieval with detailed information."""
    
    print(f"\n{'='*50}")
    print("Testing Invoice Retrieval with Details")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = InvoicesToolImpl(api_client)
        
        try:
            result = await tool.execute(
                page_size=5,
                current_item=0,
                include_payment=True,
                include_invoice_delivery=True,
                order_direction="Desc"
            )
            
            print(f"✅ Success: Retrieved {len(result['data'])} invoices with payment and delivery details")
            
            # Display detailed info for first invoice
            if result['data']:
                invoice = result['data'][0]
                print(f"\nDetailed Invoice Info:")
                print(f"  ID: {invoice['id']}")
                print(f"  Code: {invoice['code']}")
                print(f"  Total: {invoice.get('total', 'N/A')}")
                print(f"  Customer: {invoice.get('customerName', 'N/A')}")
                print(f"  Branch: {invoice.get('branchName', 'N/A')}")
                payments = invoice.get('payments', []) or []
                invoice_details = invoice.get('invoiceDetails', []) or []
                print(f"  Payments: {len(payments)} payment(s)")
                print(f"  Invoice Details: {len(invoice_details)} item(s)")
                
        except Exception as e:
            print(f"❌ Exception occurred: {str(e)}")


async def test_get_invoices_by_customer():
    """Test retrieval of invoices by customer."""
    
    print(f"\n{'='*50}")
    print("Testing Invoice Retrieval by Customer")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = InvoicesToolImpl(api_client)
        
        # First get some invoices to find customer IDs
        try:
            result = await tool.execute(page_size=10, current_item=0)
            
            if result['data']:
                # Find invoices with customer IDs
                customers_found = []
                for invoice in result['data']:
                    if invoice.get('customerId'):
                        customers_found.append({
                            'id': invoice['customerId'],
                            'name': invoice.get('customerName', 'Unknown')
                        })
                
                if customers_found:
                    # Test with first customer
                    customer = customers_found[0]
                    print(f"Testing with customer: {customer['name']} (ID: {customer['id']})")
                    
                    customer_result = await tool.execute(
                        page_size=10,
                        current_item=0,
                        customer_ids=[customer['id']],
                        order_direction="Desc"
                    )
                    
                    print(f"✅ Success: Retrieved {len(customer_result['data'])} invoices for customer {customer['name']}")
                    
                    total_amount = sum(invoice.get('total', 0) or 0 for invoice in customer_result['data'])
                    print(f"Total Amount for this customer: {total_amount:,.0f}")
                else:
                    print("⚠️  No invoices with customer IDs found")
            else:
                print("⚠️  No invoices found to test customer filtering")
                
        except Exception as e:
            print(f"❌ Exception occurred: {str(e)}")


if __name__ == "__main__":
    print("🚀 Starting get_invoices tool debug test...")
    
    asyncio.run(test_get_invoices_basic())
    asyncio.run(test_get_invoices_by_date())
    asyncio.run(test_get_invoices_by_status())
    asyncio.run(test_get_invoices_with_details())
    asyncio.run(test_get_invoices_by_customer())
    
    print("\n✅ Debug test completed!")