#!/usr/bin/env python3
"""Simple debug script for orders tool."""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from albatross_kiotviet_mcp.config import get_api_client
from albatross_kiotviet_mcp.tools.get_orders_tool_impl import OrdersToolImpl


async def test_orders_tool():
    """Test the orders tool with various parameters."""
    print("🧪 Testing Orders Tool")
    print("=" * 50)
    
    api_client = get_api_client()
    
    async with api_client:
        tool = OrdersToolImpl(api_client)
        
        # Test 1: Basic orders retrieval
        print("\n📋 Test 1: Basic orders retrieval")
        try:
            result = await tool.execute(page_size=5)
            print(f"✅ Success: Retrieved {len(result.get('data', []))} orders")
            print(f"📊 Total orders: {result.get('total', 0)}")
            
            # Show first order details if available
            if result.get('data'):
                first_order = result['data'][0]
                print(f"📄 First order: {first_order.get('code', 'N/A')} - {first_order.get('customerName', 'N/A')}")
                print(f"💰 Total: {first_order.get('total', 0):,.0f} VND")
                print(f"📅 Date: {first_order.get('purchaseDate', 'N/A')}")
                print(f"🏪 Branch: {first_order.get('branchName', 'N/A')}")
                print(f"📊 Status: {first_order.get('statusValue', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 2: Orders with payment information
        print("\n💳 Test 2: Orders with payment information")
        try:
            result = await tool.execute(
                page_size=3,
                includePayment=True
            )
            print(f"✅ Success: Retrieved {len(result.get('data', []))} orders with payment info")
            
            # Show payment details if available
            if result.get('data'):
                for order in result['data']:
                    if order.get('payments'):
                        print(f"💳 Order {order.get('code', 'N/A')} has {len(order['payments'])} payment(s)")
                        for payment in order['payments']:
                            print(f"   - {payment.get('method', 'N/A')}: {payment.get('amount', 0):,.0f} VND")
                        break
                        
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 3: Filter by status (completed orders)
        print("\n✅ Test 3: Filter by completed orders (status=3)")
        try:
            result = await tool.execute(
                page_size=3,
                status=[3]  # Status 3 = Hoàn thành
            )
            print(f"✅ Success: Retrieved {len(result.get('data', []))} completed orders")
            
            if result.get('data'):
                for order in result['data']:
                    print(f"📄 {order.get('code', 'N/A')} - Status: {order.get('statusValue', 'N/A')}")
                        
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 4: Recent orders (last 7 days)
        print("\n📅 Test 4: Recent orders (last 7 days)")
        try:
            seven_days_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            result = await tool.execute(
                page_size=5,
                lastModifiedFrom=seven_days_ago
            )
            print(f"✅ Success: Retrieved {len(result.get('data', []))} recent orders")
            print(f"📊 Total recent orders: {result.get('total', 0)}")
                        
        except Exception as e:
            print(f"❌ Error: {e}")

    print("\n🎉 Orders tool testing completed!")


if __name__ == "__main__":
    asyncio.run(test_orders_tool())