#!/usr/bin/env python3
"""Script debug đơn giản để test công cụ get_returns."""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Thêm src vào path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from albatross_kiotviet_mcp.config import get_api_client
from albatross_kiotviet_mcp.tools.get_returns_tool_impl import ReturnsToolImpl


async def test_get_returns_basic():
    """Test lấy đơn trả hàng cơ bản."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Trả Hàng Cơ Bản")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ReturnsToolImpl(api_client)
        
        try:
            # L<PERSON>y cơ bản - 10 đơn trả hàng đầu tiên
            result = await tool.execute(
                page_size=10,
                current_item=0,
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} đơn trả hàng trong tổng số {result['total']}")
            
            # Hiển thị một vài đơn trả hàng đầu tiên
            for i, return_order in enumerate(result['data'][:3]):
                print(f"\nĐơn trả hàng {i+1}:")
                print(f"  ID: {return_order['id']}")
                print(f"  Mã: {return_order['code']}")
                print(f"  Ngày trả: {return_order.get('returnDate', 'N/A')}")
                print(f"  Chi nhánh: {return_order.get('branchName', 'N/A')}")
                print(f"  Khách hàng: {return_order.get('customerName', 'N/A')} ({return_order.get('customerCode', 'N/A')})")
                print(f"  Hóa đơn gốc: {return_order.get('invoiceCode', 'N/A')}")
                print(f"  Tổng tiền: {return_order.get('total', 'N/A')}")
                print(f"  Giảm giá: {return_order.get('discount', 'N/A')}")
                print(f"  Trạng thái: {return_order.get('status', 'N/A')} ({return_order.get('statusValue', 'N/A')})")
                print(f"  Mô tả: {return_order.get('description', 'N/A')}")
                print(f"  Người bán: {return_order.get('soldByName', 'N/A')}")
                print(f"  Người tạo: {return_order.get('createdBy', 'N/A')}")
                print(f"  Ngày tạo: {return_order.get('createdDate', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")
            print(f"Loại lỗi: {type(e).__name__}")


async def test_get_returns_by_date():
    """Test lấy đơn trả hàng với lọc ngày."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Trả Hàng Theo Ngày")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ReturnsToolImpl(api_client)
        
        # Lấy đơn trả hàng từ 30 ngày trước
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        try:
            result = await tool.execute(
                page_size=20,
                current_item=0,
                from_return_date=start_date.strftime("%Y-%m-%d"),
                to_return_date=end_date.strftime("%Y-%m-%d"),
                order_by="returnDate",
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} đơn trả hàng từ {start_date.strftime('%Y-%m-%d')} đến {end_date.strftime('%Y-%m-%d')}")
            
            # Hiển thị thống kê
            if result['data']:
                total_amount = sum(return_order.get('total', 0) or 0 for return_order in result['data'])
                total_discount = sum(return_order.get('discount', 0) or 0 for return_order in result['data'])
                
                print(f"Tổng giá trị trả hàng: {total_amount:,.0f}")
                print(f"Tổng giảm giá: {total_discount:,.0f}")
                print(f"Giá trị thực tế: {(total_amount - total_discount):,.0f}")
                
                # Thống kê theo trạng thái
                status_stats = {}
                for return_order in result['data']:
                    status = return_order.get('statusValue', 'N/A')
                    if status not in status_stats:
                        status_stats[status] = 0
                    status_stats[status] += 1
                
                print("Thống kê theo trạng thái:")
                for status, count in status_stats.items():
                    print(f"  {status}: {count} đơn")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_returns_by_status():
    """Test lấy đơn trả hàng với lọc trạng thái."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Trả Hàng Theo Trạng Thái")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ReturnsToolImpl(api_client)
        
        # Test các giá trị trạng thái khác nhau (điều chỉnh theo hệ thống của bạn)
        statuses = [1, 2, 3]  # 1=Chờ duyệt, 2=Đã duyệt, 3=Đã hủy (có thể khác)
        
        for status in statuses:
            try:
                result = await tool.execute(
                    page_size=10,
                    current_item=0,
                    status=status,
                    order_direction="Desc"
                )
                
                status_names = {1: "Chờ duyệt", 2: "Đã duyệt", 3: "Đã hủy"}
                status_name = status_names.get(status, f"Trạng thái {status}")
                print(f"✅ Đơn trả hàng {status_name}: {len(result['data'])} trong tổng số {result['total']}")
                
            except Exception as e:
                print(f"❌ Lỗi cho trạng thái {status}: {str(e)}")


async def test_get_returns_by_customer():
    """Test lấy đơn trả hàng theo khách hàng."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Trả Hàng Theo Khách Hàng")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ReturnsToolImpl(api_client)
        
        try:
            # Trước tiên lấy một số đơn trả hàng để có customer_ids
            result = await tool.execute(page_size=10, current_item=0)
            
            if result['data']:
                # Lấy customer_ids từ đơn trả hàng đầu tiên
                customer_ids = [ro['customerId'] for ro in result['data'][:3] if ro.get('customerId')]
                
                if customer_ids:
                    # Loại bỏ duplicates
                    customer_ids = list(set(customer_ids))
                    print(f"Test với customer IDs: {customer_ids}")
                    
                    # Lấy đơn trả hàng theo khách hàng cụ thể
                    customer_result = await tool.execute(
                        customer_ids=customer_ids,
                        page_size=20
                    )
                    
                    print(f"✅ Thành công: Đã lấy {len(customer_result['data'])} đơn trả hàng cho các khách hàng {customer_ids}")
                    
                    # Hiển thị thống kê theo khách hàng
                    customer_stats = {}
                    for ro in customer_result['data']:
                        customer_name = ro.get('customerName', 'N/A')
                        if customer_name not in customer_stats:
                            customer_stats[customer_name] = {'count': 0, 'total': 0}
                        customer_stats[customer_name]['count'] += 1
                        customer_stats[customer_name]['total'] += ro.get('total', 0) or 0
                    
                    print("Thống kê theo khách hàng:")
                    for customer_name, stats in customer_stats.items():
                        print(f"  {customer_name}: {stats['count']} đơn, tổng {stats['total']:,.0f}")
                else:
                    print("⚠️  Không tìm thấy customer_ids để test")
            else:
                print("⚠️  Không tìm thấy đơn trả hàng để test lọc khách hàng")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_returns_by_invoice():
    """Test lấy đơn trả hàng theo hóa đơn gốc."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Trả Hàng Theo Hóa Đơn Gốc")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ReturnsToolImpl(api_client)
        
        try:
            # Trước tiên lấy một số đơn trả hàng để có invoice_ids
            result = await tool.execute(page_size=10, current_item=0)
            
            if result['data']:
                # Lấy invoice_ids từ đơn trả hàng đầu tiên
                invoice_ids = [ro['invoiceId'] for ro in result['data'][:3] if ro.get('invoiceId')]
                
                if invoice_ids:
                    # Loại bỏ duplicates
                    invoice_ids = list(set(invoice_ids))
                    print(f"Test với invoice IDs: {invoice_ids}")
                    
                    # Lấy đơn trả hàng theo hóa đơn gốc cụ thể
                    invoice_result = await tool.execute(
                        invoice_ids=invoice_ids,
                        page_size=20
                    )
                    
                    print(f"✅ Thành công: Đã lấy {len(invoice_result['data'])} đơn trả hàng cho các hóa đơn {invoice_ids}")
                    
                    # Hiển thị thông tin chi tiết
                    for ro in invoice_result['data']:
                        print(f"  Đơn trả {ro['code']}: Hóa đơn gốc {ro.get('invoiceCode', 'N/A')} - {ro.get('total', 'N/A')}")
                else:
                    print("⚠️  Không tìm thấy invoice_ids để test")
            else:
                print("⚠️  Không tìm thấy đơn trả hàng để test lọc hóa đơn")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_returns_with_details():
    """Test lấy đơn trả hàng với chi tiết."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Trả Hàng Với Chi Tiết")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ReturnsToolImpl(api_client)
        
        try:
            result = await tool.execute(
                page_size=5,
                current_item=0,
                include_return_details=True,
                order_direction="Desc"
            )
            
            print(f"✅ Thành công: Đã lấy {len(result['data'])} đơn trả hàng với chi tiết")
            
            # Hiển thị thông tin chi tiết cho đơn trả hàng đầu tiên
            if result['data']:
                ro = result['data'][0]
                print(f"\nThông tin chi tiết đơn trả hàng:")
                print(f"  ID: {ro['id']}")
                print(f"  Mã: {ro['code']}")
                print(f"  Tổng tiền: {ro.get('total', 'N/A')}")
                print(f"  Giảm giá: {ro.get('discount', 'N/A')}")
                print(f"  Tỷ lệ giảm giá: {ro.get('discountRatio', 'N/A')}")
                print(f"  Hóa đơn gốc: {ro.get('invoiceCode', 'N/A')}")
                print(f"  Khách hàng: {ro.get('customerName', 'N/A')}")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


async def test_get_specific_returns():
    """Test lấy đơn trả hàng cụ thể theo ID."""
    
    print(f"\n{'='*50}")
    print("Test Lấy Đơn Trả Hàng Cụ Thể Theo ID")
    print(f"{'='*50}")
    
    api_client = get_api_client()
    
    async with api_client:
        tool = ReturnsToolImpl(api_client)
        
        # Trước tiên lấy một số return IDs
        try:
            result = await tool.execute(page_size=5, current_item=0)
            
            if result['data']:
                # Lấy IDs từ đơn trả hàng đầu tiên
                return_ids = [ro['id'] for ro in result['data'][:3]]
                print(f"Test với return IDs: {return_ids}")
                
                # Lấy đơn trả hàng cụ thể
                specific_result = await tool.execute(ids=return_ids)
                
                print(f"✅ Thành công: Đã lấy {len(specific_result['data'])} đơn trả hàng cụ thể")
                
                for ro in specific_result['data']:
                    print(f"  Đơn {ro['id']}: {ro['code']} - {ro.get('customerName', 'N/A')} - {ro.get('total', 'N/A')}")
            else:
                print("⚠️  Không tìm thấy đơn trả hàng để test lấy ID cụ thể")
                
        except Exception as e:
            print(f"❌ Có lỗi xảy ra: {str(e)}")


if __name__ == "__main__":
    print("🚀 Bắt đầu test debug công cụ get_returns...")
    
    asyncio.run(test_get_returns_basic())
    asyncio.run(test_get_returns_by_date())
    asyncio.run(test_get_returns_by_status())
    asyncio.run(test_get_returns_by_customer())
    asyncio.run(test_get_returns_by_invoice())
    asyncio.run(test_get_returns_with_details())
    asyncio.run(test_get_specific_returns())
    
    print("\n✅ Hoàn thành test debug!")