#!/usr/bin/env python3
"""
Simple debug script cho Categories Tool.
Gọi trực tiếp vào execute() method để debug.

Usage:
    python tests/debug_categories_simple.py
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from albatross_kiotviet_mcp.config.config import Settings
from albatross_kiotviet_mcp.api.api_client import KiotVietAPIClient
from albatross_kiotviet_mcp.tools.get_categories_tool_impl import CategoriesToolImpl


async def debug_categories():
    """Debug categories tool execution step by step."""
    
    # Setup
    settings = Settings(
        kiotviet_client_id=os.getenv("KIOTVIET_CLIENT_ID", "test_client_id"),
        kiotviet_client_secret=os.getenv("KIOTVIET_CLIENT_SECRET", "test_client_secret"),
        kiotviet_retailer=os.getenv("KIOTVIET_RETAILER", "test_retailer"),
    )
    
    print("🔍 Debugging Categories Tool")
    print(f"Retailer: {settings.kiotviet_retailer}")
    print("-" * 50)
    
    # Create API client
    api_client = KiotVietAPIClient(settings)
    
    try:
        async with api_client:
            # Create tool instance
            tool = CategoriesToolImpl(api_client)
            
            print("📋 Calling tool.execute()...")
            print("   Parameters:")
            print("   - page_size: 10")
            print("   - current_item: 0") 
            print("   - order_direction: Asc")
            print("   - hierarchical_data: True")
            print()
            
            # 🚨 SET BREAKPOINT HERE để debug vào execute() method
            result = await tool.execute(
                page_size=10,
                current_item=0,
                order_direction="Asc",
                hierarchical_data=True
            )
            
            print("✅ Execution completed!")
            print(f"Result type: {type(result)}")
            print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            if isinstance(result, dict):
                print(f"Total categories: {result.get('total', 'N/A')}")
                print(f"Page size: {result.get('pageSize', 'N/A')}")
                
                data = result.get('data', [])
                print(f"Categories returned: {len(data)}")
                
                if data:
                    print("\nFirst few categories:")
                    for i, category in enumerate(data[:3]):
                        print(f"  {i+1}. {category.get('name', 'N/A')} (ID: {category.get('id', 'N/A')})")
                        if category.get('parentId'):
                            print(f"     Parent ID: {category.get('parentId')}")
            
            return result
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Print full traceback for debugging
        import traceback
        print("\nFull traceback:")
        traceback.print_exc()
        
        return None


if __name__ == "__main__":
    print("🚀 Categories Tool Debug Script")
    print("=" * 50)
    
    # Check environment variables
    if not os.getenv("KIOTVIET_CLIENT_ID"):
        print("⚠️  Set KIOTVIET_CLIENT_ID environment variable for real API testing")
    if not os.getenv("KIOTVIET_CLIENT_SECRET"):
        print("⚠️  Set KIOTVIET_CLIENT_SECRET environment variable for real API testing")
    if not os.getenv("KIOTVIET_RETAILER"):
        print("⚠️  Set KIOTVIET_RETAILER environment variable for real API testing")
    
    print()
    
    # Run debug
    result = asyncio.run(debug_categories())
    
    if result:
        print("\n🎉 Debug completed successfully!")
    else:
        print("\n💥 Debug failed - check errors above")