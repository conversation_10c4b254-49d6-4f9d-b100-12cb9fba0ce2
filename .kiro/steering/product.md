# Product Overview

## KiotViet MCP Server

A professional Model Context Protocol (MCP) server that integrates with KiotViet's retail management API. This server enables AI assistants like <PERSON> to interact with KiotViet's retail system through natural language queries.

### Core Purpose
- Provide seamless integration between AI assistants and KiotViet retail management system
- Enable natural language queries for retail data (categories, products, inventory, invoices, revenue)
- Maintain clean architecture with proper separation of concerns
- Ensure production-ready reliability with comprehensive error handling and logging

### Key Features
- **Clean Architecture**: Domain-driven design with clear layer separation
- **Secure Authentication**: Automatic OAuth2 token management with refresh handling
- **MCP Tools**: Categories, products, branches, inventory, and revenue retrieval with pagination
- **Type Safety**: Complete type hints with Pydantic validation
- **Production Ready**: Error handling, logging, retry logic, and async/await throughout
- **Extensible**: Easy-to-follow patterns for adding new tools

### Available MCP Tools
- `get_categories`: Retrieve product categories with pagination and hierarchical support
- `get_branches`: Retrieve store branches with pagination
- `get_inventory`: Retrieve product inventory/stock data with filtering
- `get_invoices_by_day`: Retrieve invoices for specific date ranges
- `calculate_daily_revenue`: Calculate revenue metrics from invoice data

### Target Users
- Retail businesses using KiotViet POS system
- AI assistant integrators
- Developers building retail analytics solutions