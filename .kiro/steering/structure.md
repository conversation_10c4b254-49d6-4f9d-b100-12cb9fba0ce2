# Project Structure & Organization

## Architecture Overview

This project follows **Clean Architecture** principles with clear separation of concerns across distinct layers:

```
src/albatross_kiotviet_mcp/
├── api/                    # Infrastructure layer - External API integration
├── config/                 # Configuration management
├── models/                 # Data models and validation
├── tools/                  # MCP tools implementation
│   ├── core/              # Base classes and middleware
│   └── schemas/           # Tool parameter schemas
├── main.py                # Application entry point
└── server.py              # FastMCP server setup
```

## Detailed Structure

### Root Level
- **`.env`**: Environment variables (not in git)
- **`.env.example`**: Environment template
- **`pyproject.toml`**: Project configuration, dependencies, and build settings
- **`uv.lock`**: Dependency lock file for reproducible builds
- **`Makefile`**: Common development commands
- **`Dockerfile`** & **`docker-compose.yml`**: Container deployment
- **`mcp.json`**: Local MCP server configuration for testing

### Source Code (`src/albatross_kiotviet_mcp/`)

#### API Layer (`api/`)
- **`api_client.py`**: Main KiotViet API client with authentication and retry logic
- **`auth.py`**: OAuth2 token management and refresh handling

#### Configuration (`config/`)
- **`config.py`**: Pydantic settings with environment variable loading
- **`dependencies.py`**: Dependency injection setup for API client

#### Models (`models/`)
- **`models.py`**: Pydantic data models for API responses (Category, Invoice, Product, Branch)

#### Tools (`tools/`)
Core MCP tool implementations:
- **`register_tools.py`**: Central tool registration with FastMCP decorators
- **`get_categories_tool_impl.py`**: Categories retrieval tool
- **`get_branches_tool_impl.py`**: Branches retrieval tool  
- **`get_inventory_tool_impl.py`**: Inventory/stock data tool
- **`get_invoices_by_day_tool_impl.py`**: Invoice retrieval by date range
- **`calculate_daily_revenue_tool_impl.py`**: Revenue calculation tool

##### Core (`tools/core/`)
- **`base_tool.py`**: Abstract base class for all tools with common validation
- **`middleware.py`**: Decorators for logging, validation, and error handling

##### Schemas (`tools/schemas/`)
Parameter validation schemas for each tool:
- **`common_schemas.py`**: Shared parameter types
- **`branches_schema.py`**: Branch tool parameters
- **`categories_schema.py`**: Category tool parameters
- **`inventory_schema.py`**: Inventory tool parameters
- **`invoices_schema.py`**: Invoice tool parameters
- **`revenue_schema.py`**: Revenue tool parameters

#### Application Entry Points
- **`main.py`**: Application startup with logging configuration
- **`server.py`**: FastMCP server initialization and tool registration

### Tests (`tests/`)
- **`debug_*_simple.py`**: Simple debug/test scripts for each tool
- Test files follow `test_*.py` naming convention
- Uses pytest with async support

### Logs (`logs/`)
- **Auto-created**: Log files with daily rotation
- **Format**: `kiotviet_mcp_YYYYMMDD.log`
- **Retention**: 7 days

## Coding Conventions

### File Organization
- **One class per file** for tool implementations
- **Descriptive filenames** ending with `_impl.py` for implementations
- **Schema files** grouped in `schemas/` subdirectory
- **Core utilities** in `core/` subdirectory

### Import Structure
```python
# Standard library imports first
from typing import Dict, Any
from datetime import datetime

# Third-party imports
from pydantic import BaseModel, Field
from loguru import logger

# Local imports (relative)
from ..config import get_api_client
from ..models.models import CategoryResponse
from .core.base_tool import BaseTool
```

### Class Organization
- **Tool classes** inherit from `BaseTool`
- **Async methods** throughout for non-blocking operations
- **Type hints** on all methods and parameters
- **Docstrings** with Args/Returns sections

### Error Handling
- **Middleware decorators** for consistent error handling
- **Specific exceptions** for different error types
- **Comprehensive logging** at appropriate levels
- **Graceful degradation** where possible

### Configuration Management
- **Environment variables** for all external configuration
- **Pydantic Settings** for validation and type safety
- **Default values** for optional settings
- **Clear separation** between required and optional config

## Extension Patterns

### Adding New Tools
1. Create tool implementation in `tools/new_tool_impl.py`
2. Create parameter schema in `tools/schemas/new_tool_schema.py`
3. Add tool registration in `tools/register_tools.py`
4. Follow existing patterns for validation and error handling

### Adding New API Endpoints
1. Add method to `api/api_client.py`
2. Create corresponding Pydantic model in `models/models.py`
3. Follow existing async patterns and error handling

### Configuration Extensions
1. Add new settings to `config/config.py`
2. Use Pydantic Field with description and defaults
3. Update `.env.example` with new variables