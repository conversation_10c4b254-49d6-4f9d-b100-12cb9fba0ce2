# Technology Stack & Build System

## Core Technologies

### Language & Runtime
- **Python 3.10+** (supports 3.10, 3.11, 3.12, 3.13)
- **Async/await** throughout for non-blocking operations

### Key Dependencies
- **FastMCP 2.2.0+**: Model Context Protocol server framework
- **httpx 0.25.0+**: Modern async HTTP client for API requests
- **Pydantic 2.0+**: Data validation and settings management with type safety
- **python-dotenv**: Environment variable management
- **loguru**: Advanced logging with rotation and formatting

### Development Tools
- **uv**: Modern Python package manager (preferred over pip)
- **pytest**: Testing framework with async support
- **black**: Code formatting (line length: 100)
- **isort**: Import sorting (black-compatible profile)
- **mypy**: Static type checking with strict settings
- **flake8**: Linting

## Build System

### Package Management
- Uses **uv** as the primary package manager (faster than pip)
- Fallback to pip supported
- Dependencies defined in `pyproject.toml` with optional groups
- Lock file: `uv.lock` for reproducible builds

### Project Structure
- **hatchling** as build backend
- Source layout: `src/albatross_kiotviet_mcp/`
- Entry point: `albatross-kiotviet-mcp-server` console script

## Common Commands

### Development Setup
```bash
# Initial setup (creates .venv, installs deps, creates .env template)
make setup

# Manual setup if make unavailable
uv venv
uv sync --extra dev --extra test
```

### Running the Server
```bash
# Using make (recommended)
make run

# Using uv directly
uv run albatross-kiotviet-mcp-server

# Development mode with auto-reload
uv run python -m albatross_kiotviet_mcp.main

# Using installed script (after uv sync)
.venv/bin/albatross-kiotviet-mcp-server
```

### Building & Distribution
```bash
# Build package
make build
# or
uv build

# Install in development mode
uv sync
```

### Testing
```bash
# Run tests
uv run pytest

# Run with coverage
uv run pytest --cov=src

# Run specific test file
uv run pytest tests/debug_revenue_simple.py
```

### Code Quality
```bash
# Format code
uv run black src/ tests/
uv run isort src/ tests/

# Type checking
uv run mypy src/

# Linting
uv run flake8 src/ tests/
```

### Cleanup
```bash
make clean
```

## Docker Support

### Build & Run
```bash
# Build image
docker build -t albatross-kiotviet-mcp .

# Run with docker-compose
docker-compose up

# Run standalone
docker run --env-file .env albatross-kiotviet-mcp
```

## Configuration

### Environment Variables
Required in `.env` file:
- `KIOTVIET_CLIENT_ID`: KiotViet API client ID
- `KIOTVIET_CLIENT_SECRET`: KiotViet API client secret  
- `KIOTVIET_RETAILER`: KiotViet retailer name

Optional settings:
- `KIOTVIET_AUTH_URL`: OAuth2 endpoint (default: https://id.kiotviet.vn/connect/token)
- `KIOTVIET_API_BASE_URL`: API base URL (default: https://public.kiotapi.com)
- `KIOTVIET_REQUEST_TIMEOUT`: Request timeout in seconds (default: 30)
- `LOG_LEVEL`: Logging level (default: INFO)

### MCP Integration
Configure in Claude Desktop's `claude_desktop_config.json`:
```json
{
  "mcpServers": {
    "kiotviet": {
      "command": "uv",
      "args": ["--directory", "/path/to/project", "run", "albatross-kiotviet-mcp-server"],
      "env": {"PYTHONUNBUFFERED": "1"}
    }
  }
}
```