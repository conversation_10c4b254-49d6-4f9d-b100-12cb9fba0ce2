# 🤖 RAG MCP Chatbot

A production-ready RAG chatbot that integrates with MCP servers using **mcp-use** library.

## 🎯 Features

- ✅ **mcp-use Integration**: Connect to any MCP server via HTTP
- ✅ **RAG System**: Vector-based knowledge retrieval
- ✅ **Smart Routing**: Automatic decision between RAG and MCP tools
- ✅ **Streaming Support**: Real-time response streaming
- ✅ **Web Interface**: Streamlit-based UI
- ✅ **API Backend**: FastAPI REST endpoints
- ✅ **Production Ready**: Error handling, logging, monitoring

## 🏗️ Architecture

```
User Query → RAG Chatbot → mcp-use → MCP Server → External APIs
     ↓              ↓
Vector Search   Tool Calling
+ Knowledge    + Function
  Base          Execution
```

## 📦 Quick Start

### 1. Installation

```bash
git clone <this-repo>
cd rag-mcp-chatbot
pip install -r requirements.txt
```

### 2. Configuration

```bash
cp .env.example .env
# Edit .env with your settings
```

### 3. Run

```bash
# Test connection
python test_connection.py

# Run chatbot
python main.py

# Web interface
streamlit run app.py

# API server
python api.py
```

## 🔧 Configuration

Configure your MCP servers in `config/mcp_config.json`:

```json
{
  "mcpServers": {
    "your-server": {
      "url": "http://localhost:8000/mcp/"
    }
  }
}
```

## 📚 Usage Examples

```python
from src.chatbot import RAGMCPChatbot

# Initialize
chatbot = RAGMCPChatbot()

# Chat
response = await chatbot.chat("Your question here")

# Streaming
async for chunk in chatbot.stream_chat("Your question"):
    print(chunk, end="")
```

## 🚀 Deployment

- **Local**: `python main.py`
- **Docker**: `docker-compose up`
- **Production**: See deployment guide

## 📖 Documentation

- [Setup Guide](docs/setup.md)
- [Configuration](docs/configuration.md)
- [API Reference](docs/api.md)
- [Deployment](docs/deployment.md)

## 🤝 Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md)

## 📄 License

MIT License - see [LICENSE](LICENSE)
