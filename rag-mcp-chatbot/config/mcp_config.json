{"mcpServers": {"kiotviet": {"url": "http://localhost:8000/mcp/", "name": "KiotViet MCP Server", "description": "KiotViet API integration server", "timeout": 30, "retry_attempts": 3}, "example": {"url": "http://localhost:8001/mcp/", "name": "Example MCP Server", "description": "Example MCP server for testing", "timeout": 30, "retry_attempts": 3, "enabled": false}}, "default_server": "kiotviet", "global_settings": {"max_steps": 15, "verbose": true, "use_server_manager": false, "disallowed_tools": []}}