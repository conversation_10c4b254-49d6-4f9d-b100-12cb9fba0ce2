d       s   i   g   n   a   t   u   r   e   "   ,       4   0   0   
   
   
   i   f       _   _   n   a   m   e   _   _       =   =       "   _   _   m   a   i   n   _   _   "   :   
                   a   p   p   .   r   u   n   (   p   o   r   t   =   8   0   0   0   )   
   `   `   `   
   
   #   #       H   a   n   d   l   i   n   g       e   r   r   o   r   s   
   
   W   h   e   n       t   h   e       l   i   b   r   a   r   y       i   s       u   n   a   b   l   e       t   o       c   o   n   n   e   c   t       t   o       t   h   e       A   P   I       (   f   o   r       e   x   a   m   p   l   e   ,       d   u   e       t   o       n   e   t   w   o   r   k       c   o   n   n   e   c   t   i   o   n       p   r   o   b   l   e   m   s       o   r       a       t   i   m   e   o   u   t   )   ,       a       s   u   b   c   l   a   s   s       o   f       `   o   p   e   n   a   i   .   A   P   I   C   o   n   n   e   c   t   i   o   n   E   r   r   o   r   `       i   s       r   a   i   s   e   d   .   
   
   W   h   e   n       t   h   e       A   P   I       r   e   t   u   r   n   s       a       n   o   n   -   s   u   c   c   e   s   s       s   t   a   t   u   s       c   o   d   e       (   t   h   a   t       i   s   ,       4   x   x       o   r       5   x   x   
   r   e   s   p   o   n   s   e   )   ,       a       s   u   b   c   l   a   s   s       o   f       `   o   p   e   n   a   i   .   A   P   I   S   t   a   t   u   s   E   r   r   o   r   `       i   s       r   a   i   s   e   d   ,       c   o   n   t   a   i   n   i   n   g       `   s   t   a   t   u   s   _   c   o   d   e   `       a   n   d       `   r   e   s   p   o   n   s   e   `       p   r   o   p   e   r   t   i   e   s   .   
   
   A   l   l       e   r   r   o   r   s       i   n   h   e   r   i   t       f   r   o   m       `   o   p   e   n   a   i   .   A   P   I   E   r   r   o   r   `   .   
   
   `   `   `   p   y   t   h   o   n   
   i   m   p   o   r   t       o   p   e   n   a   i   
   f   r   o   m       o   p   e   n   a   i       i   m   p   o   r   t       O   p   e   n   A   I   
   
   c   l   i   e   n   t       =       O   p   e   n   A   I   (   )   
   
   t   r   y   :   
                   c   l   i   e   n   t   .   f   i   n   e   _   t   u   n   i   n   g   .   j   o   b   s   .   c   r   e   a   t   e   (   
                                   m   o   d   e   l   =   "   g   p   t   -   4   o   "   ,   
                                   t   r   a   i   n   i   n   g   _   f   i   l   e   =   "   f   i   l   e   -   a   b   c   1   2   3   "   ,   
                   )   
   e   x   c   e   p   t       o   p   e   n   a   i   .   A   P   I   C   o   n   n   e   c   t   i   o   n   E   r   r   o   r       a   s       e   :   
                   p   r   i   n   t   (   "   T   h   e       s   e   r   v   e   r       c   o   u   l   d       n   o   t       b   e       r   e   a   c   h   e   d   "   )   
                   p   r   i   n   t   (   e   .   _   _   c   a   u   s   e   _   _   )           #       a   n       u   n   d   e   r   l   y   i   n   g       E   x   c   e   p   t   i   o   n   ,       l   i   k   e   l   y       r   a   i   s   e   d       w   i   t   h   i   n       h   t   t   p   x   .   
   e   x   c   e   p   t       o   p   e   n   a   i   .   R   a   t   e   L   i   m   i   t   E   r   r   o   r       a   s       e   :   
                   p   r   i   n   t   (   "   A       4   2   9       s   t   a   t   u   s       c   o   d   e       w   a   s       r   e   c   e   i   v   e   d   ;       w   e       s   h   o   u   l   d       b   a   c   k       o   f   f       a       b   i   t   .   "   )   
   e   x   c   e   p   t       o   p   e   n   a   i   .   A   P   I   S   t   a   t   u   s   E   r   r   o   r       a   s       e   :   
                   p   r   i   n   t   (   "   A   n   o   t   h   e   r       n   o   n   -   2   0   0   -   r   a   n   g   e       s   t   a   t   u   s       c   o   d   e       w   a   s       r   e   c   e   i   v   e   d   "   )   
                   p   r   i   n   t   (   e   .   s   t   a   t   u   s   _   c   o   d   e   )   
                   p   r   i   n   t   (   e   .   r   e   s   p   o   n   s   e   )   
   `   `   `   
   
   E   r   r   o   r       c   o   d   e   s       a   r   e       a   s       f   o   l   l   o   w   s   :   
   
   |       S   t   a   t   u   s       C   o   d   e       |       E   r   r   o   r       T   y   p   e                                                                       |   
   |       -   -   -   -   -   -   -   -   -   -   -       |       -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -   -       |   
   |       4   0   0                                       |       `   B   a   d   R   e   q   u   e   s   t   E   r   r   o   r   `                                           |   
   |       4   0   1                                       |       `   A   u   t   h   e   n   t   i   c   a   t   i   o   n   E   r   r   o   r   `                           |   
   |       4   0   3                                       |       `   P   e   r   m   i   s   s   i   o   n   D   e   n   i   e   d   E   r   r   o   r   `                   |   
   |       4   0   4                                       |       `   N   o   t   F   o   u   n   d   E   r   r   o   r   `                                                   |   
   |       4   2   2                                       |       `   U   n   p   r   o   c   e   s   s   a   b   l   e   E   n   t   i   t   y   E   r   r   o   r   `       |   
   |       4   2   9                                       |       `   R   a   t   e   L   i   m   i   t   E   r   r   o   r   `                                               |   
   |       >   =   5   0   0                               |       `   I   n   t   e   r   n   a   l   S   e   r   v   e   r   E   r   r   o   r   `                           |   
   |       N   /   A                                       |       `   A   P   I   C   o   n   n   e   c   t   i   o   n   E   r   r   o   r   `                               |   
   
   #   #       R   e   q   u   e   s   t       I   D   s   
   
   >       F   o   r       m   o   r   e       i   n   f   o   r   m   a   t   i   o   n       o   n       d   e   b   u   g   g   i   n   g       r   e   q   u   e   s   t   s   ,       s   e   e       [   t   h   e   s   e       d   o   c   s   ]   (   h   t   t   p   s   :   /   /   p   l   a   t   f   o   r   m   .   o   p   e   n   a   i   .   c   o   m   /   d   o   c   s   /   a   p   i   -   r   e   f   e   r   e   n   c   e   /   d   e   b   u   g   g   i   n   g   -   r   e   q   u   e   s   t   s   )   
   
   A   l   l       o   b   j   e   c   t       r   e   s   p   o   n   s   e   s       i   n       t   h   e       S   D   K       p   r   o   v   i   d   e       a       `   _   r   e   q   u   e   s   t   _   i   d   `       p   r   o   p   e   r   t   y       w   h   i   c   h       i   s       a   d   d   e   d       f   r   o   m       t   h   e       `   x   -   r   e   q   u   e   s   t   -   i   d   `       r   e   s   p   o   n   s   e       h   e   a   d   e   r       s   o       t   h   a   t       y   o   u       c   a   n       q   u   i   c   k   l   y       l   o   g       f   a   i   l   i   n   g       r   e   q   u   e   s   t   s       a   n   d       r   e   p   o   r   t       t   h   e   m       b   a   c   k       t   o       O   p   e   n   A   I   .   
   
   `   `   `   p   y   t   h   o   n   
   r   e   s   p   o   n   s   e       =       a   w   a   i   t       c   l   i   e   n   t   .   r   e   s   p   o   n   s   e   s   .   c   r   e   a   t   e   (   
                   m   o   d   e   l   =   "   g   p   t   -   4   o   -   m   i   n   i   "   ,   
                   i   n   p   u   t   =   "   S   a   y       '   t   h   i   s       i   s       a       t   e   s   t   '   .   "   ,   
   )   
   p   r   i   n   t   (   r   e   s   p   o   n   s   e   .   _   r   e   q   u   e   s   t   _   i   d   )           #       r   e   q   _   1   2   3   
   `   `   `   
   
   N   o   t   e       t   h   a   t       u   n   l   i   k   e       o   t   h   e   r       p   r   o   p   e   r   t   i   e   s       t   h   a   t       u   s   e       a   n       `   _   `       p   r   e   f   i   x   ,       t   h   e       `   _   r   e   q   u   e   s   t   _   i   d   `       p   r   o   p   e   r   t   y   
   _   i   s   _       p   u   b   l   i   c   .       U   n   l   e   s   s       d   o   c   u   m   e   n   t   e   d       o   t   h   e   r   w   i   s   e   ,       _   a   l   l   _       o   t   h   e   r       `   _   `       p   r   e   f   i   x       p   r   o   p   e   r   t   i   e   s   ,   
   m   e   t   h   o   d   s       a   n   d       m   o   d   u   l   e   s       a   r   e       _   p   r   i   v   a   t   e   _   .   
   
   >       [   !   I   M   P   O   R   T   A   N   T   ]           
   >       I   f       y   o   u       n   e   e   d       t   o       a   c   c   e   s   s       r   e   q   u   e   s   t       I   D   s       f   o   r       f   a   i   l   e   d       r   e   q   u   e   s   t   s       y   o   u       m   u   s   t       c   a   t   c   h       t   h   e       `   A   P   I   S   t   a   t   u   s   E   r   r   o   r   `       e   x   c   e   p   t   i   o   n   
   
   `   `   `   p   y   t   h   o   n   
   i   m   p   o   r   t       o   p   e   n   a   i   
   
   t   r   y   :   
                   c   o   m   p   l   e   t   i   o   n       =       a   w   a   i   t       c   l   i   e   n   t   .   c   h   a   t   .   c   o   m   p   l   e   t   i   o   n   s   .   c   r   e   a   t   e   (   
                                   m   e   s   s   a   g   e   s   =   [   {   "   r   o   l   e   "   :       "   u   s   e   r   "   ,       "   c   o   n   t   e   n   t   "   :       "   S   a   y       t   h   i   s       i   s       a       t   e   s   t   "   }   ]   ,       m   o   d   e   l   =   "   g   p   t   -   4   "   
                   )   
   e   x   c   e   p   t       o   p   e   n   a   i   .   A   P   I   S   t   a   t   u   s   E   r   r   o   r       a   s       e   x   c   :   
                   p   r   i   n   t   (   e   x   c   .   r   e   q   u   e   s   t   _   i   d   )           #       r   e   q   _   1   2   3   
                   r   a   i   s   e       e   x   c   
   `   `   `   
   
   #   #       R   e   t   r   i   e   s   
   
   C   e   r   t   a   i   n       e   r   r   o   r   s       a   r   e       a   u   t   o   m   a   t   i   c   a   l   l   y       r   e   t   r   i   e   d       2       t   i   m   e   s       b   y       d   e   f   a   u   l   t   ,       w   i   t   h       a       s   h   o   r   t       e   x   p   o   n   e   n   t   i   a   l       b   a   c   k   o   f   f   .   
   C   o   n   n   e   c   t   i   o   n       e   r   r   o   r   s       (   f   o   r       e   x   a   m   p   l   e   ,       d   u   e       t   o       a       n   e   t   w   o   r   k       c   o   n   n   e   c   t   i   v   i   t   y       p   r   o   b   l   e   m   )   ,       4   0   8       R   e   q   u   e   s   t       T   i   m   e   o   u   t   ,       4   0   9       C   o   n   f   l   i   c   t   ,   
   4   2   9       R   a   t   e       L   i   m   i   t   ,       a   n   d       >   =   5   0   0       I   n   t   e   r   n   a   l       e   r   r   o   r   s       a   r   e       a   l   l       r   e   t   r   i   e   d       b   y       d   e   f   a   u   l   t   .   
   
   Y   o   u       c   a   n       u   s   e       t   h   e       `   m   a   x   _   r   e   t   r   i   e   s   `       o   p   t   i   o   n       t   o       c   o   n   f   i   g   u   r   e       o   r       d   i   s   a   b   l   e       r   e   t   r   y       s   e   t   t   i   n   g   s   :   
   
   `   `   `   p   y   t   h   o   n   
   f   r   o   m       o   p   e   n   a   i       i   m   p   o   r   t       O   p   e   n   A   I   
   
   #       C   o   n   f   i   g   u   r   e       t   h   e       d   e   f   a   u   l   t       f   o   r       a   l   l       r   e   q   u   e   s   t   s   :   
   c   l   i   e   n   t       =       O   p   e   n   A   I   (   
                   #       d   e   f   a   u   l   t       i   s       2   
                   m   a   x   _   r   e   t   r   i   e   s   =   0   ,   
   )   
   
   #       O   r   ,       c   o   n   f   i   g   u   r   e       p   e   r   -   r   e   q   u   e   s   t   :   
   c   l   i   e   n   t   .   w   i   t   h   _   o   p   t   i   o   n   s   (   m   a   x   _   r   e   t   r   i   e   s   =   5   )   .   c   h   a   t   .   c   o   m   p   l   e   t   i   o   n   s   .   c   r   e   a   t   e   (   
                   m   e   s   s   a   g   e   s   =   [   
                                   {   
                                                   "   r   o   l   e   "   :       "   u   s   e   r   "   ,   
                                                   "   c   o   n   t   e   n   t   "   :       "   H   o   w       c   a   n       I       g   e   t       t   h   e       n   a   m   e       o   f       t   h   e       c   u   r   r   e   n   t       d   a   y       i   n       J   a   v   a   S   c   r   i   p   t   ?   "   ,   
                                   }   
                   ]   ,   
                   m   o   d   e   l   =   "   g   p   t   -   4   o   "   ,   
   )   
   `   `   `   
   
   #   #       T   i   m   e   o   u   t   s   
   
   B   y       d   e   f   a   u   l   t       r   e   q   u   e   s   t   s       t   i   m   e       o   u   t       a   f   t   e   r       1   0       m   i   n   u   t   e   s   .       Y   o   u       c   a   n       c   o   n   f   i   g   u   r   e       t   h   i   s       w   i   t   h       a       `   t   i   m   e   o   u   t   `       o   p   t   i   o   n   ,   
   w   h   i   c   h       a   c   c   e   p   t   s       a       f   l   o   a   t       o   r       a   n       [   `   h   t   t   p   x   .   T   i   m   e   o   u   t   `   ]   (   h   t   t   p   s   :   /   /   w   w   w   .   p   y   t   h   o   n   -   h   t   t   p   x   .   o   r   g   /   a   d   v   a   n   c   e   d   /   t   i   m   e   o   u   t   s   /   #   f   i   n   e   -   t   u   n   i   n   g   -   t   h   e   -   c   o   n   f   i   g   u   r   a   t   i   o   n   )       o   b   j   e   c   t   :   
   
   `   `   `   p   y   t   h   o   n   
   f   r   o   m       o   p   e   n   a   i       i   m   p   o   r   t       O   p   e   n   A   I   
   
   #       C   o   n   f   i   g   u   r   e       t   h   e       d   e   f   a   u   l   t       f   o   r       a   l   l       r   e   q   u   e   s   t   s   :   
   c   l   i   e   n   t       =       O   p   e   n   A   I   (   
                   #       2   0       s   e   c   o   n   d   s       (   d   e   f   a   u   l   t       i   s       1   0       m   i   n   u   t   e   s   )   
                   t   i   m   e   o   u   t   =   2   0   .   0   ,   
   )   
   
   #       M   o   r   e       g   r   a   n   u   l   a   r       c   o   n   t   r   o   l   :   
   c   l   i   e   n   t       =       O   p   e   n   A   I   (   
                   t   i   m   e   o   u   t   =   h   t   t   p   x   .   T   i   m   e   o   u   t   (   6   0   .   0   ,       r   e   a   d   =   5   .   0   ,       w   r   i   t   e   =   1   0   .   0   ,       c   o   n   n   e   c   t   =   2   .   0   )   ,   
   )   
   
   #       O   v   e   r   r   i   d   e       p   e   r   -   r   e   q   u   e   s   t   :   
   c   l   i   e   n   t   .   w   i   t   h   _   o   p   t   i   o   n   s   (   t   i   m   e   o   u   t   =   5   .   0   )   .   c   h   a   t   .   c   o   m   p   l   e   t   i   o   n   s   .   c   r   e   a   t   e   (   
                   m   e   s   s   a   g   e   s   =   [   
                                   {   
                                                   "   r   o   l   e   "   :       "   u   s   e   r   "   ,   
                                                   "   c   o   n   t   e   n   t   "   :       "   H   o   w       c   a   n       I       l   i   s   t       a   l   l       f   i   l   e   s       i   n       a       d   i   r   e   c   t   o   r   y       u   s   i   n   g       P   y   t   h   o   n   ?   "   ,   
                                   }   
                   ]   ,   
                   m   o   d   e   l   =   "   g   p   t   -   4   o   "   ,   
   )   
   `   `   `   
   
   O   n       t   i   m   e   o   u   t   ,       a   n       `   A   P   I   T   i   m   e   o   u   t   E   r   r   o   r   `       i   s       t   h   r   o   w   n   .   
   
   N   o   t   e       t   h   a   t       r   e   q   u   e   s   t   s       t   h   a   t       t   i   m   e       o   u   t       a   r   e       [   r   e   t   r   i   e   d       t   w   i   c   e       b   y       d   e   f   a   u   l   t   ]   (   h   t   t   p   s   :   /   /   g   i   t   h   u   b   .   c   o   m   /   o   p   e   n   a   i   /   o   p   e   n   a   i   -   p   y   t   h   o   n   /   t   r   e   e   /   m   a   i   n   /   #   r   e   t   r   i   e   s   )   .   
   
   #   #       A   d   v   a   n   c   e   d   
   
   #   #   #       L   o   g   g   i   n   g   
   
   W   e       u   s   e       t   h   e       s   t   a   n   d   a   r   d       l   i   b   r   a   r   y       [   `   l   o   g   g   i   n   g   `   ]   (   h   t   t   p   s   :   /   /   d   o   c   s   .   p   y   t   h   o   n   .   o   r   g   /   3   /   l   i   b   r   a   r   y   /   l   o   g   g   i   n   g   .   h   t   m   l   )       m   o   d   u   l   e   .   
   
   Y   o   u       c   a   n       e   n   a   b   l   e       l   o   g   g   i   n   g       b   y       s   e   t   t   i   n   g       t   h   e       e   n   v   i   r   o   n   m   e   n   t       v   a   r   i   a   b   l   e       `   O   P   E   N   A   I   _   L   O   G   `       t   o       `   i   n   f   o   `   .   
   
   `   `   `   s   h   e   l   l   
   $       e   x   p   o   r   t       O   P   E   N   A   I   _   L   O   G   =   i   n   f   o   
   `   `   `   
   
   O   r       t   o       `   d   e   b   u   g   `       f   o   r       m   o   r   e       v   e   r   b   o   s   e       l   o   g   g   i   n   g   .   
   
   #   #   #       H   o   w       t   o       t   e   l   l       w   h   e   t   h   e   r       `   N   o   n   e   `       m   e   a   n   s       `   n   u   l   l   `       o   r       m   i   s   s   i   n   g   
   
   I   n       a   n       A   P   I       r   e   s   p   o   n   s   e   ,       a       f   i   e   l   d       m   a   y       b   e       e   x   p   l   i   c   i   t   l   y       `   n   u   l   l   `   ,       o   r       m   i   s   s   i   n   g       e   n   t   i   r   e   l   y   ;       i   n       e   i   t   h   e   r       c   a   s   e   ,       i   t   s       v   a   l   u   e       i   s       `   N   o   n   e   `       i   n       t   h   i   s       l   i   b   r   a   r   y   .       Y   o   u       c   a   n       d   i   f   f   e   r   e   n   t   i   a   t   e       t   h   e       t   w   o       c   a   s   e   s       w   i   t   h       `   .   m   o   d   e   l   _   f   i   e   l   d   s   _   s   e   t   `   :   
   
   `   `   `   p   y   
   i   f       r   e   s   p   o   n   s   e   .   m   y   _   f   i   e   l   d       i   s       N   o   n   e   :   
           i   f       '   m   y   _   f   i   e   l   d   '       n   o   t       i   n       r   e   s   p   o   n   s   e   .   m   o   d   e   l   _   f   i   e   l   d   s   _   s   e   t   :   
                   p   r   i   n   t   (   '   G   o   t       j   s   o   n       l   i   k   e       {   }   ,       w   i   t   h   o   u   t       a       "   m   y   _   f   i   e   l   d   "       k   e   y       p   r   e   s   e   n   t       a   t       a   l   l   .   '   )   
           e   l   s   e   :   
                   p   r   i   n   t   (   '   G   o   t       j   s   o   n       l   i   k   e       {   "   m   y   _   f   i   e   l   d   "   :       n   u   l   l   }   .   '   )   
   `   `   `   
   
   #   #   #       A   c   c   e   s   s   i   n   g       r   a   w       r   e   s   p   o   n   s   e       d   a   t   a       (   e   .   g   .       h   e   a   d   e   r   s   )   
   
   T   h   e       "   r   a   w   "       R   e   s   p   o   n   s   e       o   b   j   e   c   t       c   a   n       b   e       a   c   c   e   s   s   e   d       b   y       p   r   e   f   i   x   i   n   g       `   .   w   i   t   h   _   r   a   w   _   r   e   s   p   o   n   s   e   .   `       t   o       a   n   y       H   T   T   P       m   e   t   h   o   d       c   a   l   l   ,       e   .   g   .   ,   
   
   `   `   `   p   y   
   f   r   o   m       o   p   e   n   a   i       i   m   p   o   r   t       O   p   e   n   A   I   
   
   c   l   i   e   n   t       =       O   p   e   n   A   I   (   )   
   r   e   s   p   o   n   s   e       =       c   l   i   e   n   t   .   c   h   a   t   .   c   o   m   p   l   e   t   i   o   n   s   .   w   i   t   h   _   r   a   w   _   r   e   s   p   o   n   s   e   .   c   r   e   a   t   e   (   
                   m   e   s   s   a   g   e   s   =   [   {   
                                   "   r   o   l   e   "   :       "   u   s   e   r   "   ,   
                                   "   c   o   n   t   e   n   t   "   :       "   S   a   y       t   h   i   s       i   s       a       t   e   s   t   "   ,   
                   }   ]   ,   
                   m   o   d   e   l   =   "   g   p   t   -   4   o   "   ,   
   )   
   p   r   i   n   t   (   r   e   s   p   o   n   s   e   .   h   e   a   d   e   r   s   .   g   e   t   (   '   X   -   M   y   -   H   e   a   d   e   r   '   )   )   
   
   c   o   m   p   l   e   t   i   o   n       =       r   e   s   p   o   n   s   e   .   p   a   r   s   e   (   )           #       g   e   t       t   h   e       o   b   j   e   c   t       t   h   a   t       `   c   h   a   t   .   c   o   m   p   l   e   t   i   o   n   s   .   c   r   e   a   t   e   (   )   `       w   o   u   l   d       h   a   v   e       r   e   t   u   r   n   e   d   
   p   r   i   n   t   (   c   o   m   p   l   e   t   i   o   n   )   
   `   `   `   
   
   T   h   e   s   e       m   e   t   h   o   d   s       r   e   t   u   r   n       a       [   `   L   e   g   a   c   y   A   P   I   R   e   s   p   o   n   s   e   `   ]   (   h   t   t   p   s   :   /   /   g   i   t   h   u   b   .   c   o   m   /   o   p   e   n   a   i   /   o   p   e   n   a   i   -   p   y   t   h   o   n   /   t   r   e   e   /   m   a   i   n   /   s   r   c   /   o   p   e   n   a   i   /   _   l   e   g   a   c   y   _   r   e   s   p   o   n   s   e   .   p   y   )       o   b   j   e   c   t   .       T   h   i   s       i   s       a       l   e   g   a   c   y       c   l   a   s   s       a   s       w   e   '   r   e       c   h   a   n   g   i   n   g       i   t       s   l   i   g   h   t   l   y       i   n       t   h   e       n   e   x   t       m   a   j   o   r       v   e   r   s   i   o   n   .   
   
   F   o   r       t   h   e       s   y   n   c       c   l   i   e   n   t       t   h   i   s       w   i   l   l       m   o   s   t   l   y       b   e       t   h   e       s   a   m   e       w   i   t   h       t   h   e       e   x   c   e   p   t   i   o   n   
   o   f       `   c   o   n   t   e   n   t   `       &       `   t   e   x   t   `       w   i   l   l       b   e       m   e   t   h   o   d   s       i   n   s   t   e   a   d       o   f       p   r   o   p   e   r   t   i   e   s   .       I   n       t   h   e   
   a   s   y   n   c       c   l   i   e   n   t   ,       a   l   l       m   e   t   h   o   d   s       w   i   l   l       b   e       a   s   y   n   c   .   
   
   A       m   i   g   r   a   t   i   o   n       s   c   r   i   p   t       w   i   l   l       b   e       p   r   o   v   i   d   e   d       &       t   h   e       m   i   g   r   a   t   i   o   n       i   n       g   e   n   e   r   a   l       s   h   o   u   l   d   
   b   e       s   m   o   o   t   h   .   
   
   #   #   #   #       `   .   w   i   t   h   _   s   t   r   e   a   m   i   n   g   _   r   e   s   p   o   n   s   e   `   
   
   T   h   e       a   b   o   v   e       i   n   t   e   r   f   a   c   e       e   a   g   e   r   l   y       r   e   a   d   s       t   h   e       f   u   l   l       r   e   s   p   o   n   s   e       b   o   d   y       w   h   e   n       y   o   u       m   a   k   e       t   h   e       r   e   q   u   e   s   t   ,       w   h   i   c   h       m   a   y       n   o   t       a   l   w   a   y   s       b   e       w   h   a   t       y   o   u       w   a   n   t   .   
   
   T   o       s   t   r   e   a   m       t   h   e       r   e   s   p   o   n   s   e       b   o   d   y   ,       u   s   e       `   .   w   i   t   h   _   s   t   r   e   a   m   i   n   g   _   r   e   s   p   o   n   s   e   `       i   n   s   t   e   a   d   ,       w   h   i   c   h       r   e   q   u   i   r   e   s       a       c   o   n   t   e   x   t       m   a   n   a   g   e   r       a   n   d       o   n   l   y       r   e   a   d   s       t   h   e       r   e   s   p   o   n   s   e       b   o   d   y       o   n   c   e       y   o   u       c   a   l   l       `   .   r   e   a   d   (   )   `   ,       `   .   t   e   x   t   (   )   `   ,       `   .   j   s   o   n   (   )   `   ,       `   .   i   t   e   r   _   b   y   t   e   s   (   )   `   ,       `   .   i   t   e   r   _   t   e   x   t   (   )   `   ,       `   .   i   t   e   r   _   l   i   n   e   s   (   )   `       o   r       `   .   p   a   r   s   e   (   )   `   .       I   n       t   h   e       a   s   y   n   c       c   l   i   e   n   t   ,       t   h   e   s   e       a   r   e       a   s   y   n   c       m   e   t   h   o   d   s   .   
   
   A   s       s   u   c   h   ,       `   .   w   i   t   h   _   s   t   r   e   a   m   i   n   g   _   r   e   s   p   o   n   s   e   `       m   e   t   h   o   d   s       r   e   t   u   r   n       a       d   i   f   f   e   r   e   n   t       [   `   A   P   I   R   e   s   p   o   n   s   e   `   ]   (   h   t   t   p   s   :   /   /   g   i   t   h   u   b   .   c   o   m   /   o   p   e   n   a   i   /   o   p   e   n   a   i   -   p   y   t   h   o   n   /   t   r   e   e   /   m   a   i   n   /   s   r   c   /   o   p   e   n   a   i   /   _   r   e   s   p   o   n   s   e   .   p   y   )       o   b   j   e   c   t   ,       a   n   d       t   h   e       a   s   y   n   c       c   l   i   e   n   t       r   e   t   u   r   n   s       a   n       [   `   A   s   y   n   c   A   P   I   R   e   s   p   o   n   s   e   `   ]   (   h   t   t   p   s   :   /   /   g   i   t   h   u   b   .   c   o   m   /   o   p   e   n   a   i   /   o   p   e   n   a   i   -   p   y   t   h   o   n   /   t   r   e   e   /   m   a   i   n   /   s   r   c   /   o   p   e   n   a   i   /   _   r   e   s   p   o   n   s   e   .   p   y   )       o   b   j   e   c   t   .   
   
   `   `   `   p   y   t   h   o   n   
   w   i   t   h       c   l   i   e   n   t   .   c   h   a   t   .   c   o   m   p   l   e   t   i   o   n   s   .   w   i   t   h   _   s   t   r   e   a   m   i   n   g   _   r   e   s   p   o   n   s   e   .   c   r   e   a   t   e   (   
                   m   e   s   s   a   g   e   s   =   [   
                                   {   
                                                   "   r   o   l   e   "   :       "   u   s   e   r   "   ,   
                                                   "   c   o   n   t   e   n   t   "   :       "   S   a   y       t   h   i   s       i   s       a       t   e   s   t   "   ,   
                                   }   
                   ]   ,   
                   m   o   d   e   l   =   "   g   p   t   -   4   o   "   ,   
   )       a   s       r   e   s   p   o   n   s   e   :   
                   p   r   i   n   t   (   r   e   s   p   o   n   s   e   .   h   e   a   d   e   r   s   .   g   e   t   (   "   X   -   M   y   -   H   e   a   d   e   r   "   )   )   
   
                   f   o   r       l   i   n   e       i   n       r   e   s   p   o   n   s   e   .   i   t   e   r   _   l   i   n   e   s   (   )   :   
                                   p   r   i   n   t   (   l   i   n   e   )   
   `   `   `   
   
   T   h   e       c   o   n   t   e   x   t       m   a   n   a   g   e   r       i   s       r   e   q   u   i   r   e   d       s   o       t   h   a   t       t   h   e       r   e   s   p   o   n   s   e       w   i   l   l       r   e   l   i   a   b   l   y       b   e       c   l   o   s   e   d   .   
   
   #   #   #       M   a   k   i   n   g       c   u   s   t   o   m   /   u   n   d   o   c   u   m   e   n   t   e   d       r   e   q   u   e   s   t   s   
   
   T   h   i   s       l   i   b   r   a   r   y       i   s       t   y   p   e   d       f   o   r       c   o   n   v   e   n   i   e   n   t       a   c   c   e   s   s       t   o       t   h   e       d   o   c   u   m   e   n   t   e   d       A   P   I   .   
   
   I   f       y   o   u       n   e   e   d       t   o       a   c   c   e   s   s       u   n   d   o   c   u   m   e   n   t   e   d       e   n   d   p   o   i   n   t   s   ,       p   a   r   a   m   s   ,       o   r       r   e   s   p   o   n   s   e       p   r   o   p   e   r   t   i   e   s   ,       t   h   e       l   i   b   r   a   r   y       c   a   n       s   t   i   l   l       b   e       u   s   e   d   .   
   
   #   #   #   #       U   n   d   o   c   u   m   e   n   t   e   d       e   n   d   p   o   i   n   t   s   
   
   T   o       m   a   k   e       r   e   q   u   e   s   t   s       t   o       u   n   d   o   c   u   m   e   n   t   e   d       e   n   d   p   o   i   n   t   s   ,       y   o   u       c   a   n       m   a   k   e       r   e   q   u   e   s   t   s       u   s   i   n   g       `   c   l   i   e   n   t   .   g   e   t   `   ,       `   c   l   i   e   n   t   .   p   o   s   t   `   ,       a   n   d       o   t   h   e   r   
   h   t   t   p       v   e   r   b   s   .       O   p   t   i   o   n   s       o   n       t   h   e       c   l   i   e   n   t       w   i   l   l       b   e       r   e   s   p   e   c   t   e   d       (   s   u   c   h       a   s       r   e   t   r   i   e   s   )       w   h   e   n       m   a   k   i   n   g       t   h   i   s       r   e   q   u   e   s   t   .   
   
   `   `   `   p   y   
   i   m   p   o   r   t       h   t   t   p   x   
   
   r   e   s   p   o   n   s   e       =       c   l   i   e   n   t   .   p   o   s   t   (   
                   "   /   f   o   o   "   ,   
                   c   a   s   t   _   t   o   =   h   t   t   p   x   .   R   e   s   p   o   n   s   e   ,   
                   b   o   d   y   =   {   "   m   y   _   p   a   r   a   m   "   :       T   r   u   e   }   ,   
   )   
   
   p   r   i   n   t   (   r   e   s   p   o   n   s   e   .   h   e   a   d   e   r   s   .   g   e   t   (   "   x   -   f   o   o   "   )   )   
   `   `   `   
   
   #   #   #   #       U   n   d   o   c   u   m   e   n   t   e   d       r   e   q   u   e   s   t       p   a   r   a   m   s   
   
   I   f       y   o   u       w   a   n   t       t   o       e   x   p   l   i   c   i   t   l   y       s   e   n   d       a   n       e   x   t   r   a       p   a   r   a   m   ,       y   o   u       c   a   n       d   o       s   o       w   i   t   h       t   h   e       `   e   x   t   r   a   _   q   u   e   r   y   `   ,       `   e   x   t   r   a   _   b   o   d   y   `   ,       a   n   d       `   e   x   t   r   a   _   h   e   a   d   e   r   s   `       r   e   q   u   e   s   t   
   o   p   t   i   o   n   s   .   
   
   #   #   #   #       U   n   d   o   c   u   m   e   n   t   e   d       r   e   s   p   o   n   s   e       p   r   o   p   e   r   t   i   e   s   
   
   T   o       a   c   c   e   s   s       u   n   d   o   c   u   m   e   n   t   e   d       r   e   s   p   o   n   s   e       p   r   o   p   e   r   t   i   e   s   ,       y   o   u       c   a   n       a   c   c   e   s   s       t   h   e       e   x   t   r   a       f   i   e   l   d   s       l   i   k   e       `   r   e   s   p   o   n   s   e   .   u   n   k   n   o   w   n   _   p   r   o   p   `   .       Y   o   u   
   c   a   n       a   l   s   o       g   e   t       a   l   l       t   h   e       e   x   t   r   a       f   i   e   l   d   s       o   n       t   h   e       P   y   d   a   n   t   i   c       m   o   d   e   l       a   s       a       d   i   c   t       w   i   t   h   
   [   `   r   e   s   p   o   n   s   e   .   m   o   d   e   l   _   e   x   t   r   a   `   ]   (   h   t   t   p   s   :   /   /   d   o   c   s   .   p   y   d   a   n   t   i   c   .   d   e   v   /   l   a   t   e   s   t   /   a   p   i   /   b   a   s   e   _   m   o   d   e   l   /   #   p   y   d   a   n   t   i   c   .   B   a   s   e   M   o   d   e   l   .   m   o   d   e   l   _   e   x   t   r   a   )   .   
   
   #   #   #       C   o   n   f   i   g   u   r   i   n   g       t   h   e       H   T   T   P       c   l   i   e   n   t   
   
   Y   o   u       c   a   n       d   i   r   e   c   t   l   y       o   v   e   r   r   i   d   e       t   h   e       [   h   t   t   p   x       c   l   i   e   n   t   ]   (   h   t   t   p   s   :   /   /   w   w   w   .   p   y   t   h   o   n   -   h   t   t   p   x   .   o   r   g   /   a   p   i   /   #   c   l   i   e   n   t   )       t   o       c   u   s   t   o   m   i   z   e       i   t       f   o   r       y   o   u   r       u   s   e       c   a   s   e   ,       i   n   c   l   u   d   i   n   g   :   
   
   -       S   u   p   p   o   r   t       f   o   r       [   p   r   o   x   i   e   s   ]   (   h   t   t   p   s   :   /   /   w   w   w   .   p   y   t   h   o   n   -   h   t   t   p   x   .   o   r   g   /   a   d   v   a   n   c   e   d   /   p   r   o   x   i   e   s   /   )   
   -       C   u   s   t   o   m       [   t   r   a   n   s   p   o   r   t   s   ]   (   h   t   t   p   s   :   /   /   w   w   w   .   p   y   t   h   o   n   -   h   t   t   p   x   .   o   r   g   /   a   d   v   a   n   c   e   d   /   t   r   a   n   s   p   o   r   t   s   /   )   
   -       A   d   d   i   t   i   o   n   a   l       [   a   d   v   a   n   c   e   d   ]   (   h   t   t   p   s   :   /   /   w   w   w   .   p   y   t   h   o   n   -   h   t   t   p   x   .   o   r   g   /   a   d   v   a   n   c   e   d   /   c   l   i   e   n   t   s   /   )       f   u   n   c   t   i   o   n   a   l   i   t   y   
   
   `   `   `   p   y   t   h   o   n   
   i   m   p   o   r   t       h   t   t   p   x   
   f   r   o   m       o   p   e   n   a   i       i   m   p   o   r   t       O   p   e   n   A   I   ,       D   e   f   a   u   l   t   H   t   t   p   x   C   l   i   e   n   t   
   
   c   l   i   e   n   t       =       O   p   e   n   A   I   (   
                   #       O   r       u   s   e       t   h   e       `   O   P   E   N   A   I   _   B   A   S   E   _   U   R   L   `       e   n   v       v   a   r   
                   b   a   s   e   _   u   r   l   =   "   h   t   t   p   :   /   /   m   y   .   t   e   s   t   .   s   e   r   v   e   r   .   e   x   a   m   p   l   e   .   c   o   m   :   8   0   8   3   /   v   1   "   ,   
                   h   t   t   p   _   c   l   i   e   n   t   =   D   e   f   a   u   l   t   H   t   t   p   x   C   l   i   e   n   t   (   
                                   p   r   o   x   y   =   "   h   t   t   p   :   /   /   m   y   .   t   e   s   t   .   p   r   o   x   y   .   e   x   a   m   p   l   e   .   c   o   m   "   ,   
                                   t   r   a   n   s   p   o   r   t   =   h   t   t   p   x   .   H   T   T   P   T   r   a   n   s   p   o   r   t   (   l   o   c   a   l   _   a   d   d   r   e   s   s   =   "   0   .   0   .   0   .   0   "   )   ,   
                   )   ,   
   )   
   `   `   `   
   
   Y   o   u       c   a   n       a   l   s   o       c   u   s   t   o   m   i   z   e       t   h   e       c   l   i   e   n   t       o   n       a       p   e   r   -   r   e   q   u   e   s   t       b   a   s   i   s       b   y       u   s   i   n   g       `   w   i   t   h   _   o   p   t   i   o   n   s   (   )   `   :   
   
   `   `   `   p   y   t   h   o   n   
   c   l   i   e   n   t   .   w   i   t   h   _   o   p   t   i   o   n   s   (   h   t   t   p   _   c   l   i   e   n   t   =   D   e   f   a   u   l   t   H   t   t   p   x   C   l   i   e   n   t   (   .   .   .   )   )   
   `   `   `   
   
   #   #   #       M   a   n   a   g   i   n   g       H   T   T   P       r   e   s   o   u   r   c   e   s   
   
   B   y       d   e   f   a   u   l   t       t   h   e       l   i   b   r   a   r   y       c   l   o   s   e   s       u   n   d   e   r   l   y   i   n   g       H   T   T   P       c   o   n   n   e   c   t   i   o   n   s       w   h   e   n   e   v   e   r       t   h   e       c   l   i   e   n   t       i   s       [   g   a   r   b   a   g   e       c   o   l   l   e   c   t   e   d   ]   (   h   t   t   p   s   :   /   /   d   o   c   s   .   p   y   t   h   o   n   .   o   r   g   /   3   /   r   e   f   e   r   e   n   c   e   /   d   a   t   a   m   o   d   e   l   .   h   t   m   l   #   o   b   j   e   c   t   .   _   _   d   e   l   _   _   )   .       Y   o   u       c   a   n       m   a   n   u   a   l   l   y       c   l   o   s   e       t   h   e       c   l   i   e   n   t       u   s   i   n   g       t   h   e       `   .   c   l   o   s   e   (   )   `       m   e   t   h   o   d       i   f       d   e   s   i   r   e   d   ,       o   r       w   i   t   h       a       c   o   n   t   e   x   t       m   a   n   a   g   e   r       t   h   a   t       c   l   o   s   e   s       w   h   e   n       e   x   i   t   i   n   g   .   
   
   `   `   `   p   y   
   f   r   o   m       o   p   e   n   a   i       i   m   p   o   r   t       O   p   e   n   A   I   
   
   w   i   t   h       O   p   e   n   A   I   (   )       a   s       c   l   i   e   n   t   :   
           #       m   a   k   e       r   e   q   u   e   s   t   s       h   e   r   e   
           .   .   .   
   
   #       H   T   T   P       c   l   i   e   n   t       i   s       n   o   w       c   l   o   s   e   d   
   `   `   `   
   
   #   #       M   i   c   r   o   s   o   f   t       A   z   u   r   e       O   p   e   n   A   I   
   
   T   o       u   s   e       t   h   i   s       l   i   b   r   a   r   y       w   i   t   h       [   A   z   u   r   e       O   p   e   n   A   I   ]   (   h   t   t   p   s   :   /   /   l   e   a   r   n   .   m   i   c   r   o   s   o   f   t   .   c   o   m   /   a   z   u   r   e   /   a   i   -   s   e   r   v   i   c   e   s   /   o   p   e   n   a   i   /   o   v   e   r   v   i   e   w   )   ,       u   s   e       t   h   e       `   A   z   u   r   e   O   p   e   n   A   I   `   
   c   l   a   s   s       i   n   s   t   e   a   d       o   f       t   h   e       `   O   p   e   n   A   I   `       c   l   a   s   s   .   
   
   >       [   !   I   M   P   O   R   T   A   N   T   ]   
   >       T   h   e       A   z   u   r   e       A   P   I       s   h   a   p   e       d   i   f   f   e   r   