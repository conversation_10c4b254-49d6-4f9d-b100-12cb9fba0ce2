# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# MCP Server Configuration
MCP_SERVER_URL=http://localhost:8000/mcp/
MCP_SERVER_HEALTH_URL=http://localhost:8000/health
MCP_SERVER_NAME=default

# RAG Configuration
VECTOR_DB_TYPE=chroma
VECTOR_DB_PATH=./data/vectordb
EMBEDDING_MODEL=text-embedding-3-small
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# LLM Settings
DEFAULT_LLM_PROVIDER=openai
DEFAULT_LLM_MODEL=gpt-4o
LLM_TEMPERATURE=0.1
MAX_TOKENS=2000

# Application Settings
LOG_LEVEL=INFO
DEBUG=False
MAX_CONVERSATION_HISTORY=50

# Web Interface
STREAMLIT_PORT=8501
API_PORT=8080

# Performance
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
RETRY_ATTEMPTS=3
