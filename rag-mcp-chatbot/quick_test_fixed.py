#!/usr/bin/env python3
"""
Quick test after fixing streaming issues.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.chatbot import RAGMCPChatbot


async def test_basic_functionality():
    """Test basic chatbot functionality."""
    print("🧪 Testing Fixed RAG MCP Chatbot")
    print("=" * 40)
    
    try:
        # Initialize chatbot
        print("🚀 Initializing chatbot...")
        chatbot = RAGMCPChatbot()
        await chatbot.initialize()
        print("✅ Chatbot initialized!")
        
        # Test regular chat
        print("\n💬 Testing regular chat...")
        query = "List available MCP tools"
        response = await chatbot.chat(query)
        print(f"✅ Regular chat works!")
        print(f"Response preview: {response[:100]}...")
        
        # Test streaming chat
        print("\n🔄 Testing streaming chat...")
        query = "What tools do you have?"
        print("Streaming response: ", end="", flush=True)
        
        full_response = ""
        async for chunk in chatbot.stream_chat(query):
            print(chunk, end="", flush=True)
            full_response += chunk
        
        print(f"\n✅ Streaming works! ({len(full_response)} chars)")
        
        # Test system info
        print("\n📊 Testing system info...")
        info = await chatbot.get_system_info()
        print(f"✅ System info: {info.get('chatbot_initialized', False)}")
        
        print("\n🎉 All tests passed!")
        
        # Cleanup
        await chatbot.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def interactive_test():
    """Simple interactive test."""
    print("\n🎯 Interactive Test Mode")
    print("Type 'quit' to exit")
    print("-" * 30)
    
    chatbot = RAGMCPChatbot()
    
    try:
        await chatbot.initialize()
        print("✅ Ready for questions!")
        
        while True:
            query = input("\nYou: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            print("Bot: ", end="", flush=True)
            
            try:
                # Try streaming
                async for chunk in chatbot.stream_chat(query):
                    print(chunk, end="", flush=True)
                print()  # New line
                
            except Exception as e:
                print(f"\n❌ Error: {e}")
    
    finally:
        await chatbot.cleanup()
        print("👋 Goodbye!")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        asyncio.run(interactive_test())
    else:
        success = asyncio.run(test_basic_functionality())
        sys.exit(0 if success else 1)
