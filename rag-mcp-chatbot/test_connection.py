#!/usr/bin/env python3
"""
Test script to verify MCP server connection and basic functionality.
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.config import get_config, get_mcp_config
from src.mcp_client import MCPClientManager
from src.rag import RAGSystem


async def test_configuration():
    """Test configuration loading."""
    print("🔧 Testing Configuration...")
    print("-" * 30)
    
    try:
        # Test main config
        config = get_config()
        print(f"✅ Main config loaded")
        print(f"   LLM Provider: {config.default_llm_provider}")
        print(f"   LLM Model: {config.default_llm_model}")
        print(f"   Vector DB: {config.vector_db_type}")
        
        # Test MCP config
        mcp_config = get_mcp_config()
        print(f"✅ MCP config loaded")
        print(f"   Default server: {mcp_config.default_server}")
        print(f"   Servers: {list(mcp_config.mcpServers.keys())}")
        
        # Check API keys
        if config.openai_api_key:
            print(f"✅ OpenAI API key: {'*' * 10}{config.openai_api_key[-4:]}")
        else:
            print("⚠️ OpenAI API key not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


async def test_mcp_connection():
    """Test MCP server connection."""
    print("\n🔗 Testing MCP Connection...")
    print("-" * 30)
    
    try:
        # Initialize MCP manager
        mcp_manager = MCPClientManager()
        await mcp_manager.initialize()
        
        print("✅ MCP manager initialized")
        
        # Test connection to servers
        connection_results = await mcp_manager.test_connection()
        
        for server_name, result in connection_results.items():
            status = result.get('status', 'unknown')
            if status == 'connected':
                print(f"✅ {server_name}: Connected")
                print(f"   URL: {result.get('url', 'unknown')}")
            elif status == 'disabled':
                print(f"⚪ {server_name}: Disabled")
            else:
                print(f"❌ {server_name}: {status}")
                if 'error' in result:
                    print(f"   Error: {result['error']}")
        
        # Test basic functionality
        print("\n🧪 Testing basic MCP functionality...")
        try:
            response = await mcp_manager.chat("List available tools briefly")
            print(f"✅ Basic MCP test successful")
            print(f"   Response preview: {response[:100]}...")
            
        except Exception as e:
            print(f"❌ Basic MCP test failed: {e}")
            return False
        
        # Cleanup
        await mcp_manager.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ MCP connection error: {e}")
        return False


async def test_rag_system():
    """Test RAG system."""
    print("\n📚 Testing RAG System...")
    print("-" * 30)
    
    try:
        # Initialize RAG system
        rag_system = RAGSystem()
        await rag_system.initialize()
        
        print("✅ RAG system initialized")
        
        # Get stats
        stats = await rag_system.get_stats()
        print(f"✅ Knowledge base stats:")
        print(f"   Documents: {stats.get('total_documents', 0)}")
        print(f"   Embedding model: {stats.get('embedding_model', 'unknown')}")
        print(f"   Vector DB path: {stats.get('vector_db_path', 'unknown')}")
        
        # Test search
        print("\n🔍 Testing knowledge search...")
        context = await rag_system.search("Business Intelligence là gì?", k=2)
        
        if context:
            print(f"✅ Search successful")
            print(f"   Context length: {len(context)} characters")
            print(f"   Preview: {context[:150]}...")
        else:
            print("⚠️ No search results found")
        
        # Cleanup
        rag_system.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ RAG system error: {e}")
        return False


async def test_full_integration():
    """Test full chatbot integration."""
    print("\n🤖 Testing Full Integration...")
    print("-" * 30)
    
    try:
        from src.chatbot import RAGMCPChatbot
        
        # Initialize chatbot
        chatbot = RAGMCPChatbot()
        await chatbot.initialize()
        
        print("✅ Full chatbot initialized")
        
        # Test queries
        test_queries = [
            "Business Intelligence là gì?",  # RAG query
            "List available MCP tools"       # MCP query
        ]
        
        for query in test_queries:
            print(f"\n📝 Testing query: {query}")
            try:
                response = await chatbot.chat(query)
                print(f"✅ Query successful")
                print(f"   Response length: {len(response)} characters")
                print(f"   Preview: {response[:100]}...")
                
            except Exception as e:
                print(f"❌ Query failed: {e}")
                return False
        
        # Get system info
        print(f"\n📊 System information:")
        info = await chatbot.get_system_info()
        print(f"   Conversations: {info.get('conversation_count', 0)}")
        print(f"   Chatbot ready: {info.get('chatbot_initialized', False)}")
        
        # Cleanup
        await chatbot.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Full integration error: {e}")
        return False


async def main():
    """Main test function."""
    print("🧪 RAG MCP Chatbot - Connection Test")
    print("=" * 50)
    
    # Setup logging
    logger.remove()
    logger.add(
        sys.stderr,
        format="<level>{level: <8}</level> | {message}",
        level="WARNING"  # Reduce log noise during testing
    )
    
    # Run tests
    tests = [
        ("Configuration", test_configuration),
        ("MCP Connection", test_mcp_connection),
        ("RAG System", test_rag_system),
        ("Full Integration", test_full_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print("-" * 20)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Run: python main.py demo")
        print("2. Run: python main.py interactive")
        print("3. Run: streamlit run app.py")
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Make sure your MCP server is running")
        print("2. Check your .env configuration")
        print("3. Verify API keys are correct")
    
    return passed == len(results)


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
