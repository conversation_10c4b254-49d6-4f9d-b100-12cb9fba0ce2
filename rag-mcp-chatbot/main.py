#!/usr/bin/env python3
"""
Main entry point for RAG MCP Chatbot.
"""

import asyncio
import sys
from pathlib import Path
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.chatbot import RAGMCPChatbot
from src.config import get_config


async def interactive_chat():
    """Interactive chat session."""
    print("🤖 RAG MCP Chatbot - Interactive Mode")
    print("=" * 50)
    print("Commands:")
    print("  /help    - Show help")
    print("  /info    - Show system info")
    print("  /tools   - List available tools")
    print("  /clear   - Clear conversation history")
    print("  /quit    - Exit")
    print("=" * 50)
    
    # Initialize chatbot
    chatbot = RAGMCPChatbot()
    
    try:
        print("🚀 Initializing chatbot...")
        await chatbot.initialize()
        print("✅ Chatbot ready! Type your questions below.\n")
        
        while True:
            try:
                # Get user input
                query = input("You: ").strip()
                
                if not query:
                    continue
                
                # Handle commands
                if query.startswith('/'):
                    await handle_command(query, chatbot)
                    continue
                
                # Process regular query
                print("Bot: ", end="", flush=True)
                
                # Use streaming for better UX
                response_parts = []
                async for chunk in chatbot.stream_chat(query):
                    print(chunk, end="", flush=True)
                    response_parts.append(chunk)
                
                print("\n")  # New line after response
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except EOFError:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                logger.error(f"Interactive chat error: {e}")
    
    finally:
        await chatbot.cleanup()


async def handle_command(command: str, chatbot: RAGMCPChatbot):
    """Handle special commands."""
    cmd = command.lower().strip()
    
    if cmd == '/help':
        print("""
Available commands:
  /help    - Show this help message
  /info    - Show system information
  /tools   - List available MCP tools
  /clear   - Clear conversation history
  /quit    - Exit the chatbot

You can also ask any questions about business, revenue, inventory, etc.
The chatbot will automatically decide whether to use knowledge base or MCP tools.
        """)
    
    elif cmd == '/info':
        print("📊 Getting system information...")
        info = await chatbot.get_system_info()
        
        print("\n🔍 System Information:")
        print(f"  Chatbot initialized: {info.get('chatbot_initialized', False)}")
        print(f"  Conversation count: {info.get('conversation_count', 0)}")
        
        if 'config' in info:
            config = info['config']
            print(f"  LLM: {config.get('llm_provider', 'unknown')} / {config.get('llm_model', 'unknown')}")
            print(f"  Vector DB: {config.get('vector_db_type', 'unknown')}")
        
        if 'mcp_status' in info:
            print("\n🔗 MCP Server Status:")
            for server, status in info['mcp_status'].items():
                status_icon = "✅" if status.get('status') == 'connected' else "❌"
                print(f"  {status_icon} {server}: {status.get('status', 'unknown')}")
        
        if 'rag_stats' in info:
            rag_stats = info['rag_stats']
            print(f"\n📚 Knowledge Base:")
            print(f"  Documents: {rag_stats.get('total_documents', 0)}")
            print(f"  Embedding model: {rag_stats.get('embedding_model', 'unknown')}")
    
    elif cmd == '/tools':
        print("🔧 Getting available tools...")
        tools_info = await chatbot.list_available_tools()
        
        if 'tools_info' in tools_info:
            print(f"\n🛠️ Available Tools:\n{tools_info['tools_info']}")
        else:
            print(f"❌ Error getting tools: {tools_info}")
    
    elif cmd == '/clear':
        chatbot.clear_conversation_history()
        print("🗑️ Conversation history cleared!")
    
    elif cmd == '/quit':
        print("👋 Goodbye!")
        sys.exit(0)
    
    else:
        print(f"❓ Unknown command: {command}")
        print("Type /help for available commands.")


async def demo_queries():
    """Run demo queries to showcase functionality."""
    print("🎯 RAG MCP Chatbot - Demo Mode")
    print("=" * 50)
    
    # Initialize chatbot
    chatbot = RAGMCPChatbot()
    
    try:
        print("🚀 Initializing chatbot...")
        await chatbot.initialize()
        print("✅ Chatbot ready!\n")
        
        # Demo queries
        demo_queries_list = [
            "Business Intelligence là gì?",  # Should use RAG
            "Lấy danh sách categories từ MCP server",  # Should use MCP
            "Doanh thu tuần trước là bao nhiêu?",  # Should use MCP
            "Cách tính tồn kho hiệu quả?",  # Should use RAG
            "Phân tích doanh thu theo chi nhánh trong tháng này"  # Should use both
        ]
        
        for i, query in enumerate(demo_queries_list, 1):
            print(f"📝 Demo Query {i}: {query}")
            print("-" * 40)
            
            try:
                response = await chatbot.chat(query)
                print(f"🤖 Response: {response}")
                
            except Exception as e:
                print(f"❌ Error: {e}")
            
            print("\n" + "=" * 50 + "\n")
            
            # Small delay between queries
            await asyncio.sleep(1)
        
        print("🎉 Demo completed!")
        
    finally:
        await chatbot.cleanup()


def main():
    """Main function."""
    config = get_config()
    
    # Setup logging
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level=config.log_level
    )
    
    print("🚀 RAG MCP Chatbot")
    print("=" * 30)
    
    # Check command line arguments
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == "demo":
            asyncio.run(demo_queries())
        elif mode == "interactive" or mode == "chat":
            asyncio.run(interactive_chat())
        else:
            print(f"❓ Unknown mode: {mode}")
            print("Available modes: demo, interactive")
            sys.exit(1)
    else:
        # Default to interactive mode
        asyncio.run(interactive_chat())


if __name__ == "__main__":
    main()
