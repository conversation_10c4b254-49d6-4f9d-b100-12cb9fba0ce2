#!/usr/bin/env python3
"""
Streamlit web interface for RAG MCP Chatbot.
"""

import streamlit as st
import asyncio
import sys
from pathlib import Path
import time
from typing import Dict, Any

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.chatbot import RAGMCPChatbot
from src.config import get_config

# Page config
st.set_page_config(
    page_title="RAG MCP Chatbot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
.main-header {
    text-align: center;
    color: #1f77b4;
    margin-bottom: 2rem;
}
.chat-message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}
.user-message {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}
.bot-message {
    background-color: #f1f8e9;
    border-left: 4px solid #4caf50;
}
.system-info {
    background-color: #fff3e0;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid #ff9800;
}
.metric-card {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'chatbot' not in st.session_state:
    st.session_state.chatbot = None
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'is_initialized' not in st.session_state:
    st.session_state.is_initialized = False
if 'system_info' not in st.session_state:
    st.session_state.system_info = {}

@st.cache_resource
def initialize_chatbot():
    """Initialize the RAG MCP chatbot."""
    try:
        config = get_config()
        chatbot = RAGMCPChatbot(config)
        return chatbot, None
    except Exception as e:
        return None, str(e)

async def get_system_info(chatbot):
    """Get system information."""
    try:
        return await chatbot.get_system_info()
    except Exception as e:
        return {"error": str(e)}

def main():
    """Main Streamlit app."""
    
    # Header
    st.markdown('<h1 class="main-header">🤖 RAG MCP Chatbot</h1>', unsafe_allow_html=True)
    st.markdown("*Intelligent chatbot with RAG knowledge base and MCP tool integration*")
    st.markdown("---")
    
    # Sidebar
    with st.sidebar:
        st.header("⚙️ System Control")
        
        # Initialize chatbot
        if not st.session_state.is_initialized:
            if st.button("🚀 Initialize Chatbot", type="primary"):
                with st.spinner("Initializing chatbot..."):
                    chatbot, error = initialize_chatbot()
                    if chatbot:
                        # Run async initialization
                        try:
                            asyncio.run(chatbot.initialize())
                            st.session_state.chatbot = chatbot
                            st.session_state.is_initialized = True
                            st.success("✅ Chatbot initialized!")
                            st.rerun()
                        except Exception as e:
                            st.error(f"❌ Initialization failed: {e}")
                    else:
                        st.error(f"❌ Failed to create chatbot: {error}")
        else:
            st.success("✅ Chatbot Ready")
            
            # System info
            if st.button("📊 Refresh System Info"):
                if st.session_state.chatbot:
                    with st.spinner("Getting system info..."):
                        info = asyncio.run(get_system_info(st.session_state.chatbot))
                        st.session_state.system_info = info
                        st.rerun()
        
        st.markdown("---")
        
        # Chat settings
        st.subheader("🎛️ Chat Settings")
        use_streaming = st.checkbox("Enable Streaming", value=True)
        show_intent = st.checkbox("Show Query Intent", value=False)
        max_history = st.slider("Max History", 10, 100, 50)
        
        st.markdown("---")
        
        # Quick actions
        st.subheader("⚡ Quick Actions")
        
        if st.button("🗑️ Clear History"):
            st.session_state.chat_history = []
            if st.session_state.chatbot:
                st.session_state.chatbot.clear_conversation_history()
            st.rerun()
        
        if st.button("🔧 List Tools"):
            if st.session_state.chatbot:
                with st.spinner("Getting tools..."):
                    tools = asyncio.run(st.session_state.chatbot.list_available_tools())
                    st.session_state.tools_info = tools
        
        st.markdown("---")
        
        # Sample queries
        st.subheader("💡 Sample Queries")
        sample_queries = [
            "Business Intelligence là gì?",
            "Lấy danh sách categories",
            "Doanh thu tuần trước",
            "Cách tính tồn kho?",
            "Phân tích theo chi nhánh"
        ]
        
        for query in sample_queries:
            if st.button(f"📝 {query}", key=f"sample_{hash(query)}"):
                st.session_state.sample_query = query
                st.rerun()
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("💬 Chat Interface")
        
        if not st.session_state.is_initialized:
            st.info("👈 Please initialize the chatbot using the sidebar.")
            st.stop()
        
        # Chat input
        query_input = st.text_input(
            "Ask me anything:",
            value=st.session_state.get('sample_query', ''),
            key="chat_input",
            placeholder="Type your question here..."
        )
        
        # Clear sample query after use
        if 'sample_query' in st.session_state:
            del st.session_state.sample_query
        
        # Send button
        if st.button("🚀 Send", type="primary") and query_input:
            if st.session_state.chatbot:
                # Add user message
                st.session_state.chat_history.append({
                    'type': 'user',
                    'content': query_input,
                    'timestamp': time.time()
                })
                
                # Process query
                with st.spinner("🤖 Processing..."):
                    try:
                        if use_streaming:
                            # Try streaming response
                            try:
                                response_placeholder = st.empty()
                                full_response = ""

                                async def stream_response():
                                    nonlocal full_response
                                    async for chunk in st.session_state.chatbot.stream_chat(query_input):
                                        full_response += chunk
                                        response_placeholder.markdown(f"🤖 **Bot:** {full_response}")

                                asyncio.run(stream_response())
                                response = full_response
                            except Exception as e:
                                # Fall back to regular response
                                st.warning("⚠️ Streaming failed, using regular mode...")
                                response = asyncio.run(st.session_state.chatbot.chat(query_input))
                        else:
                            # Regular response
                            response = asyncio.run(st.session_state.chatbot.chat(query_input))
                        
                        # Add bot response
                        st.session_state.chat_history.append({
                            'type': 'bot',
                            'content': response,
                            'timestamp': time.time()
                        })
                        
                        # Limit history
                        if len(st.session_state.chat_history) > max_history * 2:
                            st.session_state.chat_history = st.session_state.chat_history[-max_history * 2:]
                        
                    except Exception as e:
                        st.error(f"❌ Error: {e}")
                
                st.rerun()
        
        # Display chat history
        st.subheader("📜 Conversation")
        
        if st.session_state.chat_history:
            for message in reversed(st.session_state.chat_history[-20:]):  # Show last 20 messages
                if message['type'] == 'user':
                    st.markdown(f"""
                    <div class="chat-message user-message">
                        <strong>👤 You:</strong><br>
                        {message['content']}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="chat-message bot-message">
                        <strong>🤖 Bot:</strong><br>
                        {message['content']}
                    </div>
                    """, unsafe_allow_html=True)
        else:
            st.info("💡 Start a conversation by typing a question above!")
    
    with col2:
        st.subheader("📊 System Status")
        
        # System metrics
        if st.session_state.system_info:
            info = st.session_state.system_info
            
            # Basic metrics
            col_a, col_b = st.columns(2)
            with col_a:
                st.metric("💬 Conversations", info.get('conversation_count', 0))
            with col_b:
                ready = info.get('chatbot_initialized', False)
                st.metric("🟢 Status", "Ready" if ready else "Not Ready")
            
            # MCP Status
            if 'mcp_status' in info:
                st.subheader("🔗 MCP Servers")
                for server, status in info['mcp_status'].items():
                    status_text = status.get('status', 'unknown')
                    if status_text == 'connected':
                        st.success(f"✅ {server}")
                    elif status_text == 'disabled':
                        st.info(f"⚪ {server} (disabled)")
                    else:
                        st.error(f"❌ {server}")
            
            # RAG Stats
            if 'rag_stats' in info:
                rag_stats = info['rag_stats']
                st.subheader("📚 Knowledge Base")
                st.metric("📄 Documents", rag_stats.get('total_documents', 0))
                st.text(f"Model: {rag_stats.get('embedding_model', 'unknown')}")
            
            # Config info
            if 'config' in info:
                config = info['config']
                st.subheader("⚙️ Configuration")
                st.text(f"LLM: {config.get('llm_provider', 'unknown')}")
                st.text(f"Model: {config.get('llm_model', 'unknown')}")
        
        else:
            st.info("Click 'Refresh System Info' to see status")
        
        # Tools info
        if hasattr(st.session_state, 'tools_info'):
            st.subheader("🔧 Available Tools")
            tools = st.session_state.tools_info
            if 'tools_info' in tools:
                st.text_area("Tools", tools['tools_info'], height=200)
            else:
                st.error("Could not retrieve tools")
        
        # Chat statistics
        if st.session_state.chat_history:
            st.subheader("📈 Chat Stats")
            user_msgs = len([m for m in st.session_state.chat_history if m['type'] == 'user'])
            bot_msgs = len([m for m in st.session_state.chat_history if m['type'] == 'bot'])
            
            st.metric("👤 User Messages", user_msgs)
            st.metric("🤖 Bot Responses", bot_msgs)


if __name__ == "__main__":
    main()
