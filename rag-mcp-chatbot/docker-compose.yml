version: '3.8'

services:
  rag-mcp-chatbot:
    build: .
    container_name: rag-mcp-chatbot
    ports:
      - "8501:8501"  # Streamlit web interface
      - "8080:8080"  # FastAPI backend
    environment:
      # LLM Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      
      # MCP Server Configuration
      - MCP_SERVER_URL=${MCP_SERVER_URL:-http://host.docker.internal:8000/mcp/}
      - MCP_SERVER_HEALTH_URL=${MCP_SERVER_HEALTH_URL:-http://host.docker.internal:8000/health}
      - MCP_SERVER_NAME=${MCP_SERVER_NAME:-kiotviet}
      
      # RAG Configuration
      - VECTOR_DB_TYPE=${VECTOR_DB_TYPE:-chroma}
      - VECTOR_DB_PATH=${VECTOR_DB_PATH:-/app/data/vectordb}
      - EMBEDDING_MODEL=${EMBEDDING_MODEL:-text-embedding-3-small}
      - CHUNK_SIZE=${CHUNK_SIZE:-1000}
      - CHUNK_OVERLAP=${CHUNK_OVERLAP:-200}
      
      # LLM Settings
      - DEFAULT_LLM_PROVIDER=${DEFAULT_LLM_PROVIDER:-openai}
      - DEFAULT_LLM_MODEL=${DEFAULT_LLM_MODEL:-gpt-4o}
      - LLM_TEMPERATURE=${LLM_TEMPERATURE:-0.1}
      - MAX_TOKENS=${MAX_TOKENS:-2000}
      
      # Application Settings
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-False}
      - MAX_CONVERSATION_HISTORY=${MAX_CONVERSATION_HISTORY:-50}
      
      # Web Interface
      - STREAMLIT_PORT=${STREAMLIT_PORT:-8501}
      - API_PORT=${API_PORT:-8080}
      
      # Performance
      - MAX_CONCURRENT_REQUESTS=${MAX_CONCURRENT_REQUESTS:-10}
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-30}
      - RETRY_ATTEMPTS=${RETRY_ATTEMPTS:-3}
    
    volumes:
      # Persist vector database
      - ./data:/app/data
      # Mount config for easy updates
      - ./config:/app/config
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    
    depends_on:
      - mcp-server
    
    networks:
      - rag-mcp-network

  # Optional: Include MCP server in the same compose
  mcp-server:
    image: your-mcp-server:latest  # Replace with your MCP server image
    container_name: mcp-server
    ports:
      - "8000:8000"
    environment:
      - MCP_TRANSPORT=http
      - MCP_HOST=0.0.0.0
      - MCP_PORT=8000
      - MCP_PATH=/mcp/
    networks:
      - rag-mcp-network
    # Add your MCP server specific configuration here

networks:
  rag-mcp-network:
    driver: bridge

volumes:
  vector_data:
    driver: local
