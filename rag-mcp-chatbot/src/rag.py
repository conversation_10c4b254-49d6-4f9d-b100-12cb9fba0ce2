"""
RAG System for knowledge retrieval and management.
"""

import os
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger

from langchain.vectorstores import Chroma
from langchain.embeddings import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from .config import Config, get_config


class RAGSystem:
    """RAG system for knowledge retrieval."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        
        self.embeddings = None
        self.vectorstore = None
        self.text_splitter = None
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize RAG system components."""
        if self._initialized:
            return
        
        logger.info("📚 Initializing RAG system...")
        
        try:
            # Initialize embeddings
            logger.info("🔤 Setting up embeddings...")
            self.embeddings = OpenAIEmbeddings(
                model=self.config.embedding_model,
                api_key=self.config.openai_api_key
            )
            
            # Initialize vector store
            logger.info("🗄️ Setting up vector store...")
            persist_directory = Path(self.config.vector_db_path)
            persist_directory.mkdir(parents=True, exist_ok=True)
            
            self.vectorstore = Chroma(
                embedding_function=self.embeddings,
                persist_directory=str(persist_directory)
            )
            
            # Initialize text splitter
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.config.chunk_size,
                chunk_overlap=self.config.chunk_overlap,
                separators=["\n\n", "\n", " ", ""]
            )
            
            # Load default knowledge if vector store is empty
            await self._ensure_knowledge_base()
            
            self._initialized = True
            logger.info("✅ RAG system initialized successfully!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize RAG system: {e}")
            raise
    
    async def _ensure_knowledge_base(self) -> None:
        """Ensure knowledge base has some content."""
        try:
            # Check if vector store has documents
            existing_docs = self.vectorstore.get()
            
            if not existing_docs['ids']:
                logger.info("📖 Loading default knowledge base...")
                await self.load_default_knowledge()
            else:
                logger.info(f"📚 Knowledge base contains {len(existing_docs['ids'])} documents")
                
        except Exception as e:
            logger.warning(f"⚠️ Could not check existing documents: {e}")
            # Try to load default knowledge anyway
            await self.load_default_knowledge()
    
    async def load_default_knowledge(self) -> None:
        """Load default business knowledge."""
        default_knowledge = [
            {
                "title": "Business Intelligence Basics",
                "content": """
                Business Intelligence (BI) là quá trình thu thập, xử lý và phân tích dữ liệu 
                để hỗ trợ ra quyết định kinh doanh. BI bao gồm các công cụ và phương pháp 
                để chuyển đổi dữ liệu thô thành thông tin có ý nghĩa và hữu ích.
                
                Các thành phần chính của BI:
                - Thu thập dữ liệu từ nhiều nguồn
                - Làm sạch và chuẩn hóa dữ liệu
                - Phân tích và tạo báo cáo
                - Trực quan hóa dữ liệu
                - Hỗ trợ ra quyết định
                """
            },
            {
                "title": "Revenue Analysis",
                "content": """
                Phân tích doanh thu là việc đánh giá hiệu quả tài chính của doanh nghiệp 
                thông qua việc theo dõi và phân tích các chỉ số doanh thu.
                
                Các chỉ số quan trọng:
                - Doanh thu theo thời gian (ngày, tuần, tháng, năm)
                - Doanh thu theo sản phẩm/dịch vụ
                - Doanh thu theo kênh bán hàng
                - Tỷ lệ tăng trưởng doanh thu
                - Doanh thu trung bình trên khách hàng (ARPU)
                
                Phương pháp phân tích:
                - So sánh theo thời gian
                - Phân tích xu hướng
                - Phân đoạn khách hàng
                - Dự báo doanh thu
                """
            },
            {
                "title": "Inventory Management",
                "content": """
                Quản lý tồn kho là quá trình giám sát và kiểm soát hàng tồn kho để 
                đảm bảo cân bằng giữa chi phí lưu kho và mức độ phục vụ khách hàng.
                
                Các khái niệm cơ bản:
                - Tồn kho đầu kỳ: Số lượng hàng có sẵn đầu kỳ
                - Nhập kho: Hàng hóa được bổ sung vào kho
                - Xuất kho: Hàng hóa được bán hoặc sử dụng
                - Tồn kho cuối kỳ: Số lượng hàng còn lại cuối kỳ
                
                Chỉ số quan trọng:
                - Vòng quay tồn kho
                - Thời gian tồn kho trung bình
                - Tỷ lệ hàng tồn kho chết
                - Chi phí lưu kho
                """
            }
        ]
        
        documents = []
        for item in default_knowledge:
            doc = Document(
                page_content=item["content"].strip(),
                metadata={
                    "title": item["title"],
                    "source": "default_knowledge",
                    "type": "business_knowledge"
                }
            )
            documents.append(doc)
        
        # Split documents
        splits = self.text_splitter.split_documents(documents)
        
        # Add to vector store
        self.vectorstore.add_documents(splits)
        
        logger.info(f"📚 Added {len(splits)} knowledge chunks to vector store")
    
    async def search(self, query: str, k: int = 3) -> str:
        """Search for relevant knowledge."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Perform similarity search
            docs = self.vectorstore.similarity_search(query, k=k)
            
            if not docs:
                return ""
            
            # Combine results
            context_parts = []
            for doc in docs:
                title = doc.metadata.get("title", "Unknown")
                content = doc.page_content
                context_parts.append(f"[{title}]\n{content}")
            
            context = "\n\n".join(context_parts)
            
            logger.info(f"📖 Found {len(docs)} relevant documents for query")
            return context
            
        except Exception as e:
            logger.error(f"❌ Error searching knowledge base: {e}")
            return ""
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> None:
        """Add new documents to knowledge base."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Convert to Document objects
            docs = []
            for doc_data in documents:
                doc = Document(
                    page_content=doc_data["content"],
                    metadata=doc_data.get("metadata", {})
                )
                docs.append(doc)
            
            # Split documents
            splits = self.text_splitter.split_documents(docs)
            
            # Add to vector store
            self.vectorstore.add_documents(splits)
            
            logger.info(f"📚 Added {len(splits)} new document chunks")
            
        except Exception as e:
            logger.error(f"❌ Error adding documents: {e}")
            raise
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get knowledge base statistics."""
        if not self._initialized:
            await self.initialize()
        
        try:
            existing_docs = self.vectorstore.get()
            
            return {
                "total_documents": len(existing_docs['ids']),
                "vector_db_path": self.config.vector_db_path,
                "embedding_model": self.config.embedding_model,
                "chunk_size": self.config.chunk_size,
                "chunk_overlap": self.config.chunk_overlap
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting stats: {e}")
            return {"error": str(e)}
    
    def cleanup(self) -> None:
        """Clean up resources."""
        # Chroma handles persistence automatically
        self._initialized = False
        logger.info("✅ RAG system cleanup completed")
