"""
RAG MCP Chatbot - A production-ready chatbot with MCP integration.
"""

__version__ = "1.0.0"
__author__ = "Your Name"
__description__ = "RAG chatbot with MCP server integration using mcp-use"

from .chatbot import RAGMCPChatbot
from .config import Config
from .rag import RAGSystem
from .mcp_client import MCPClientManager

__all__ = [
    "RAGMCPChatbot",
    "Config", 
    "RAGSystem",
    "MCPClientManager"
]
