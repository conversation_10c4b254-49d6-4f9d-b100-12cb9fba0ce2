"""
Main RAG MCP Chatbot implementation.
"""

import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from loguru import logger

from .config import Config, get_config
from .mcp_client import MCPClientManager
from .rag import RAGSystem


class RAGMCPChatbot:
    """Main chatbot class combining RAG and MCP capabilities."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()
        
        # Initialize components
        self.mcp_manager = MCPClientManager(config=self.config)
        self.rag_system = RAGSystem(config=self.config)
        
        # Conversation history
        self.conversation_history = []
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize all chatbot components."""
        if self._initialized:
            return
        
        logger.info("🚀 Initializing RAG MCP Chatbot...")
        
        try:
            # Initialize MCP manager
            await self.mcp_manager.initialize()
            
            # Initialize RAG system
            await self.rag_system.initialize()
            
            self._initialized = True
            logger.info("✅ RAG MCP Chatbot initialized successfully!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize chatbot: {e}")
            raise
    
    def analyze_query_intent(self, query: str) -> Dict[str, bool]:
        """Analyze query to determine processing strategy."""
        query_lower = query.lower()
        
        # Keywords suggesting need for real-time data (MCP tools)
        realtime_keywords = [
            # Vietnamese
            'doanh thu', 'bán hàng', 'tồn kho', 'khách hàng', 'đơn hàng',
            'chi nhánh', 'cửa hàng', 'hóa đơn', 'thanh toán', 'sản phẩm',
            'danh mục', 'inventory', 'nhân viên', 'báo cáo', 'thống kê',
            # English
            'revenue', 'sales', 'inventory', 'customer', 'order',
            'branch', 'store', 'invoice', 'payment', 'product',
            'category', 'staff', 'report', 'statistics'
        ]
        
        # Keywords suggesting need for knowledge base (RAG)
        knowledge_keywords = [
            # Vietnamese
            'là gì', 'định nghĩa', 'giải thích', 'cách tính', 'hướng dẫn',
            'quy trình', 'phương pháp', 'nguyên tắc', 'khái niệm',
            # English
            'what is', 'definition', 'explain', 'how to', 'guide',
            'process', 'method', 'principle', 'concept'
        ]
        
        # Time-related keywords (usually need MCP)
        time_keywords = [
            'hôm nay', 'hôm qua', 'tuần này', 'tuần trước', 'tháng này',
            'tháng trước', 'năm này', 'năm ngoái', 'hiện tại', 'gần đây',
            'today', 'yesterday', 'this week', 'last week', 'this month',
            'last month', 'this year', 'last year', 'current', 'recent'
        ]
        
        needs_realtime = (
            any(keyword in query_lower for keyword in realtime_keywords) or
            any(keyword in query_lower for keyword in time_keywords)
        )
        
        needs_knowledge = (
            any(keyword in query_lower for keyword in knowledge_keywords) or
            not needs_realtime  # Default to knowledge if unclear
        )
        
        return {
            'needs_realtime_data': needs_realtime,
            'needs_knowledge_context': needs_knowledge,
            'is_complex_query': len(query.split()) > 5,
            'has_time_reference': any(keyword in query_lower for keyword in time_keywords)
        }
    
    async def chat(self, query: str) -> str:
        """Process chat query with intelligent routing."""
        if not self._initialized:
            await self.initialize()
        
        logger.info(f"💬 Processing query: {query[:100]}...")
        
        try:
            # Analyze query intent
            intent = self.analyze_query_intent(query)
            logger.info(f"🧠 Query intent: {intent}")
            
            # Get RAG context if needed
            context = ""
            if intent['needs_knowledge_context']:
                logger.info("📚 Searching knowledge base...")
                context = await self.rag_system.search(query, k=3)
                if context:
                    logger.info(f"📖 Found relevant context ({len(context)} chars)")
            
            # Prepare enhanced query
            if context:
                enhanced_query = f"""
Kiến thức nền tảng:
{context}

Câu hỏi của người dùng: {query}

Hãy sử dụng kiến thức nền tảng và các công cụ có sẵn để trả lời câu hỏi một cách đầy đủ và chính xác.
Nếu cần dữ liệu thời gian thực, hãy sử dụng các công cụ MCP để lấy thông tin mới nhất.
"""
            else:
                enhanced_query = query
            
            # Process with MCP agent
            logger.info("🤖 Processing with MCP agent...")
            response = await self.mcp_manager.chat(enhanced_query)
            
            # Store in conversation history
            self.conversation_history.append({
                'query': query,
                'response': response,
                'intent': intent,
                'used_context': bool(context),
                'timestamp': asyncio.get_event_loop().time()
            })
            
            # Limit conversation history
            if len(self.conversation_history) > self.config.max_conversation_history:
                self.conversation_history = self.conversation_history[-self.config.max_conversation_history:]
            
            logger.info("✅ Query processed successfully")
            return response
            
        except Exception as e:
            error_msg = f"❌ Error processing query: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    async def stream_chat(self, query: str) -> AsyncGenerator[str, None]:
        """Process chat query with streaming response."""
        if not self._initialized:
            await self.initialize()
        
        logger.info(f"🔄 Streaming query: {query[:100]}...")
        
        try:
            # Quick intent analysis and context retrieval
            intent = self.analyze_query_intent(query)
            context = ""
            
            if intent['needs_knowledge_context']:
                context = await self.rag_system.search(query, k=3)
            
            # Prepare enhanced query
            enhanced_query = f"""
Kiến thức nền tảng:
{context}

Câu hỏi: {query}

Trả lời dựa trên kiến thức và sử dụng công cụ nếu cần.
""" if context else query
            
            # Stream response from MCP agent
            full_response = ""
            async for chunk in self.mcp_manager.stream_chat(enhanced_query):
                full_response += chunk
                yield chunk
            
            # Store in conversation history
            self.conversation_history.append({
                'query': query,
                'response': full_response,
                'intent': intent,
                'used_context': bool(context),
                'timestamp': asyncio.get_event_loop().time()
            })
            
        except Exception as e:
            error_msg = f"❌ Streaming error: {str(e)}"
            logger.error(error_msg)
            yield error_msg
    
    async def get_system_info(self) -> Dict[str, Any]:
        """Get system information and status."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Get MCP connection status
            mcp_status = await self.mcp_manager.test_connection()
            
            # Get RAG system stats
            rag_stats = await self.rag_system.get_stats()
            
            return {
                "chatbot_initialized": self._initialized,
                "conversation_count": len(self.conversation_history),
                "mcp_status": mcp_status,
                "rag_stats": rag_stats,
                "config": {
                    "llm_provider": self.config.default_llm_provider,
                    "llm_model": self.config.default_llm_model,
                    "vector_db_type": self.config.vector_db_type,
                    "max_conversation_history": self.config.max_conversation_history
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting system info: {e}")
            return {"error": str(e)}
    
    async def list_available_tools(self) -> Dict[str, Any]:
        """List available MCP tools."""
        if not self._initialized:
            await self.initialize()
        
        return await self.mcp_manager.list_tools()
    
    def clear_conversation_history(self) -> None:
        """Clear conversation history."""
        self.conversation_history.clear()
        logger.info("🗑️ Conversation history cleared")
    
    async def add_knowledge(self, documents: list) -> None:
        """Add new documents to knowledge base."""
        if not self._initialized:
            await self.initialize()
        
        await self.rag_system.add_documents(documents)
        logger.info(f"📚 Added {len(documents)} documents to knowledge base")
    
    async def cleanup(self) -> None:
        """Clean up all resources."""
        logger.info("🧹 Cleaning up RAG MCP Chatbot...")
        
        try:
            # Cleanup MCP manager
            await self.mcp_manager.cleanup()
            
            # Cleanup RAG system
            self.rag_system.cleanup()
            
            # Clear conversation history
            self.conversation_history.clear()
            
            self._initialized = False
            logger.info("✅ Chatbot cleanup completed")
            
        except Exception as e:
            logger.error(f"⚠️ Error during cleanup: {e}")
    
    def __del__(self):
        """Destructor to ensure cleanup."""
        if self._initialized:
            try:
                asyncio.create_task(self.cleanup())
            except:
                pass
