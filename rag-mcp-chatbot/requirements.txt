# Core MCP Integration
mcp-use>=1.3.7

# LLM and RAG
langchain>=0.1.0
langchain-openai>=0.1.0
langchain-anthropic>=0.1.0
langchain-community>=0.1.0
openai>=1.0.0

# Vector Database
chromadb>=0.4.0
faiss-cpu>=1.7.0

# Web Framework
streamlit>=1.28.0
fastapi>=0.100.0
uvicorn>=0.20.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
loguru>=0.7.0
httpx>=0.25.0
requests>=2.31.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# Development
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
