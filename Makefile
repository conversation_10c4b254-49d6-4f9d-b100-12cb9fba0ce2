.PHONY: help install setup run run-http run-stdio dev test test-http build docker-build docker-run docker-stop clean logs

# Default target
help:
	@echo "🚀 KiotViet MCP Server - Available Commands:"
	@echo ""
	@echo "📦 Setup:"
	@echo "  install      - Install dependencies with uv"
	@echo "  setup        - Setup environment (install + create .env)"
	@echo "  build        - Build the package"
	@echo ""
	@echo "🖥️  Run Server:"
	@echo "  run          - Run server in HTTP mode (default)"
	@echo "  run-http     - Run server with HTTP transport"
	@echo "  run-stdio    - Run server with STDIO transport"
	@echo "  dev          - Run in development mode with auto-reload"
	@echo ""
	@echo "🐳 Docker:"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run with Docker Compose"
	@echo "  docker-stop  - Stop Docker containers"
	@echo ""
	@echo "🧪 Testing:"
	@echo "  test         - Test server connectivity"
	@echo "  test-http    - Test HTTP endpoints"
	@echo ""
	@echo "📊 Monitoring:"
	@echo "  logs         - Show real-time logs"
	@echo ""
	@echo "🧹 Maintenance:"
	@echo "  clean        - Clean build artifacts"

# Install dependencies
install:
	@echo "📦 Installing dependencies..."
	uv sync

# Setup environment
setup: install
	@echo "🔧 Setting up environment..."
	@if [ ! -f .env ]; then \
		echo "📝 Creating .env file from template..."; \
		cp env.example .env; \
		echo "✅ .env file created. Please edit it with your KiotViet credentials."; \
	else \
		echo "✅ .env file already exists."; \
	fi
	@echo "📁 Creating logs directory..."
	@mkdir -p logs
	@echo "✅ Setup complete! Edit .env file with your credentials."

# Build package
build:
	@echo "🔨 Building package..."
	uv build

# Run server in HTTP mode (default)
run: run-http

# Run server with HTTP transport
run-http:
	@echo "🌐 Starting server with HTTP transport..."
	@echo "📍 Server will be available at: http://localhost:8000/mcp/"
	@echo "📋 Press Ctrl+C to stop"
	@echo ""
	@export MCP_TRANSPORT=http && uv run python -m albatross_kiotviet_mcp.main

# Run server with STDIO transport
run-stdio:
	@echo "💻 Starting server with STDIO transport..."
	@echo "📋 Press Ctrl+C to stop"
	@echo ""
	@export MCP_TRANSPORT=stdio && uv run python -m albatross_kiotviet_mcp.main

# Development mode with auto-reload
dev:
	@echo "🔧 Starting development mode..."
	@echo "📍 Server will be available at: http://localhost:8000/mcp/"
	@echo "🔄 Auto-reload enabled"
	@echo "📋 Press Ctrl+C to stop"
	@echo ""
	@export MCP_TRANSPORT=http && export LOG_LEVEL=DEBUG && uv run python -m albatross_kiotviet_mcp.main

# Build Docker image
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t albatross-kiotviet-mcp .

# Run with Docker Compose
docker-run:
	@echo "🐳 Starting with Docker Compose..."
	@echo "📍 Server will be available at: http://localhost:8000/mcp/"
	docker-compose up -d
	@echo "✅ Docker containers started. Use 'make logs' to view logs."

# Stop Docker containers
docker-stop:
	@echo "🐳 Stopping Docker containers..."
	docker-compose down

# Test server connectivity
test:
	@echo "🧪 Testing server connectivity..."
	@echo "📍 Testing: http://localhost:8000/mcp/"
	@if curl -s http://localhost:8000/mcp/ > /dev/null 2>&1; then \
		echo "✅ Server is running and accessible"; \
	else \
		echo "❌ Server is not running or not accessible"; \
		echo "💡 Start server with: make run-http"; \
	fi

# Test HTTP endpoints
test-http:
	@echo "🧪 Testing HTTP endpoints..."
	@echo "📍 Testing MCP initialize request..."
	@curl -X POST http://localhost:8000/mcp/ \
		-H "Content-Type: application/json" \
		-H "Accept: application/json, text/event-stream" \
		-d '{"jsonrpc": "2.0", "id": "test-1", "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"tools": {}}, "clientInfo": {"name": "test-client", "version": "1.0.0"}}}' \
		2>/dev/null | head -1 || echo "❌ Server not responding"

# Show real-time logs
logs:
	@echo "📊 Showing real-time logs..."
	@echo "📋 Press Ctrl+C to stop"
	@echo ""
	@tail -f logs/kiotviet_mcp_$(shell date +%Y%m%d).log

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf dist/
	rm -rf build/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete
	@echo "✅ Clean complete"


