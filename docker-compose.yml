version: '3.8'

services:
  kiotviet-mcp:
    build: .
    container_name: albatross-kiotviet-mcp
    environment:
      # KiotViet API credentials - set these in your .env file or environment
      - KIOTVIET_CLIENT_ID=0c041bb3-9fe7-4ce7-b90b-312ddf67b8a5
      - KIOTVIET_CLIENT_SECRET=DEF35E9B62A6D9CDAFFD8679B78B6A7B66D02321
      - KIOTVIET_RETAILER=ecofoods
      
      # Optional configuration
      - KIOTVIET_AUTH_URL=${KIOTVIET_AUTH_URL:-https://id.kiotviet.vn/connect/token}
      - KIOTVIET_API_BASE_URL=${KIOTVIET_API_BASE_URL:-https://public.kiotapi.com}
      - KIOTVIET_TOKEN_BUFFER_SECONDS=${KIOTVIET_TOKEN_BUFFER_SECONDS:-300}
      - KIOTVIET_REQUEST_TIMEOUT=${KIOTVIET_REQUEST_TIMEOUT:-30}
      - KIOTVIET_MAX_RETRIES=${KIOTVIET_MAX_RETRIES:-3}

      # MCP Transport configuration (Streamable HTTP)
      - MCP_TRANSPORT=${MCP_TRANSPORT:-http}
      - MCP_HOST=${MCP_HOST:-0.0.0.0}
      - MCP_PORT=${MCP_PORT:-8000}
      - MCP_PATH=${MCP_PATH:-/mcp/}
      - LOG_LEVEL=${LOG_LEVEL:-info}

      # Python configuration
      - PYTHONUNBUFFERED=1
    volumes:
      # Mount logs directory for persistence
      - ./logs:/app/logs
    ports:
      # Expose HTTP port for Streamable HTTP protocol
      - "8000:8000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
