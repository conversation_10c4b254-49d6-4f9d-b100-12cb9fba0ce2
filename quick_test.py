#!/usr/bin/env python3
"""
Quick Test Script for mcp-use + KiotViet MCP Integration
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment
load_dotenv('.env_demo')

async def test_mcp_connection():
    """Quick test of MCP connection."""
    print("🧪 Quick Test: mcp-use + KiotViet MCP")
    print("=" * 50)
    
    try:
        # Import required modules
        from mcp_use import MCPAgent, MCPClient
        from langchain_openai import ChatOpenAI
        
        print("✅ Imports successful")
        
        # Check environment variables
        openai_key = os.getenv("OPENAI_API_KEY")
        mcp_url = os.getenv("KIOTVIET_MCP_URL", "http://localhost:8000/mcp/")
        
        if not openai_key:
            print("❌ OPENAI_API_KEY not found in environment")
            return False
        
        print(f"✅ OpenAI API key: {'*' * 10}{openai_key[-4:]}")
        print(f"✅ MCP URL: {mcp_url}")
        
        # Test MCP server health
        try:
            import requests
            health_url = os.getenv("KIOTVIET_MCP_HEALTH_URL", "http://localhost:8000/health")
            response = requests.get(health_url, timeout=5)
            if response.status_code == 200:
                print("✅ MCP server is healthy")
                print(f"   Server info: {response.json()}")
            else:
                print(f"⚠️ MCP server returned status {response.status_code}")
        except Exception as e:
            print(f"❌ Cannot reach MCP server: {e}")
            print("   Make sure KiotViet MCP server is running at http://localhost:8000")
            return False
        
        # Create MCP configuration
        config = {
            "mcpServers": {
                "kiotviet": {
                    "url": mcp_url
                }
            }
        }
        
        # Initialize components
        print("\n🔧 Initializing components...")
        client = MCPClient.from_dict(config)
        llm = ChatOpenAI(model="gpt-4o", temperature=0.1)
        agent = MCPAgent(llm=llm, client=client, max_steps=5)
        
        print("✅ MCP client created")
        print("✅ LLM initialized")
        print("✅ MCP agent created")
        
        # Test basic functionality
        print("\n🚀 Testing basic functionality...")
        
        try:
            result = await agent.run("List the available tools briefly")
            print(f"✅ Tool listing successful:")
            print(f"   {result[:200]}...")
            
            # Test a simple KiotViet query
            print("\n🏪 Testing KiotViet integration...")
            result = await agent.run("Get categories from KiotViet (just show the first few)")
            print(f"✅ KiotViet query successful:")
            print(f"   {result[:200]}...")
            
            print("\n🎉 All tests passed! mcp-use integration is working correctly.")
            return True
            
        except Exception as e:
            print(f"❌ Functionality test failed: {e}")
            return False
        
        finally:
            # Cleanup
            try:
                await client.close_all_sessions()
            except:
                pass
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Run: pip install -r requirements_demo.txt")
        return False
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rag_integration():
    """Quick test of RAG integration."""
    print("\n🧠 Quick Test: RAG Integration")
    print("=" * 50)
    
    try:
        from demo_rag_mcp_chatbot import RAGMCPChatbot
        
        print("✅ RAG chatbot import successful")
        
        # Initialize chatbot
        print("🔧 Initializing RAG + MCP chatbot...")
        chatbot = RAGMCPChatbot()
        
        print("✅ RAG + MCP chatbot initialized")
        
        # Test RAG query
        print("\n📚 Testing RAG query...")
        result = await chatbot.chat("Doanh thu là gì?")
        print(f"✅ RAG query successful:")
        print(f"   {result[:200]}...")
        
        # Test MCP query
        print("\n🔧 Testing MCP query...")
        result = await chatbot.chat("Lấy danh sách categories từ KiotViet")
        print(f"✅ MCP query successful:")
        print(f"   {result[:200]}...")
        
        print("\n🎉 RAG integration test passed!")
        
        # Cleanup
        await chatbot.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ RAG integration test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 mcp-use + KiotViet MCP Integration Test")
    print("=" * 60)
    
    # Check Python version
    if sys.version_info < (3, 11):
        print(f"❌ Python 3.11+ required, got {sys.version}")
        return
    
    print(f"✅ Python version: {sys.version}")
    
    # Run tests
    success = True
    
    # Test 1: Basic MCP connection
    success &= asyncio.run(test_mcp_connection())
    
    # Test 2: RAG integration (if basic test passed)
    if success:
        success &= asyncio.run(test_rag_integration())
    
    # Final result
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! Ready to run the full demo.")
        print("\nNext steps:")
        print("1. Run: python demo_basic_mcp_use.py")
        print("2. Run: python demo_rag_mcp_chatbot.py")
        print("3. Run: streamlit run demo_streamlit_app.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Make sure KiotViet MCP server is running")
        print("2. Check your .env file configuration")
        print("3. Install dependencies: pip install -r requirements_demo.txt")

if __name__ == "__main__":
    main()
